import { AbstractSchema, type BaseSchema } from './index'

export interface IconProps {
  name?: string
  size?: string | number
  color?: string
  type?: 'font' | 'svg' | 'image'
  fontFamily?: string
  rotate?: number
  spin?: boolean
}

export interface IconSchema extends BaseSchema {
  compName: 'icon'
  props?: IconProps
}

export class Icon extends AbstractSchema {
  component: string = 'Icon'
  name: string = '图标'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  template?: string | undefined; 
  
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return '<i class="icon-element {{ $data["props"]->name ?? "icon-home" }}" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" style="font-size: {{ $data["props"]->size ?? "16px" }}; color: {{ $data["props"]->color ?? "#333333" }}; font-family: {{ $data["props"]->fontFamily ?? "inherit" }}; transform: rotate({{ $data["props"]->rotate ?? 0 }}deg); @if($data["props"]->spin ?? false) animation: spin 1s linear infinite; @endif display: inline-block; line-height: 1;"></i>'
  }
}

export default new Icon([
  {
    ref: 'name',
    label: '图标名称',
    format: 'string',
    default: 'icon-home',
    bindable: true,
  },
  {
    ref: 'size',
    label: '图标大小',
    format: 'string',
    default: '16px',
    bindable: true,
  },
  {
    ref: 'color',
    label: '图标颜色',
    format: 'string',
    default: '#333333',
    bindable: true,
  },
  {
    ref: 'type',
    label: '图标类型',
    format: 'string',
    default: 'font',
    bindable: true,
  },
  {
    ref: 'rotate',
    label: '旋转角度',
    format: 'number',
    default: 0,
    bindable: true,
  },
  {
    ref: 'fontFamily',
    label: '字体族',
    format: 'string',
    default: 'inherit',
    bindable: true,
  },
  {
    ref: 'spin',
    label: '是否旋转',
    format: 'boolean',
    default: false,
    bindable: true,
  }
])
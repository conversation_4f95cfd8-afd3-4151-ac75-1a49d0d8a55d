import React from 'react'
import { FlowSection, AbsoluteSection } from 'lcanvas'

// 组件注册表
const componentRegistry: Record<string, React.ComponentType<any>> = {
  // 原生 HTML 组件
  'div': 'div' as any,
  'span': 'span' as any,
  'button': 'button' as any,
  'input': 'input' as any,
  'img': 'img' as any,
  'form': 'form' as any,
  'select': 'select' as any,
  'option': 'option' as any,
  'textarea': 'textarea' as any,
  'label': 'label' as any,
  'p': 'p' as any,
  'h1': 'h1' as any,
  'h2': 'h2' as any,
  'h3': 'h3' as any,
  'h4': 'h4' as any,
  'h5': 'h5' as any,
  'h6': 'h6' as any,
  'ul': 'ul' as any,
  'ol': 'ol' as any,
  'li': 'li' as any,
  'a': 'a' as any,
  
  // 自定义组件
  'FlowSection': FlowSection,
  'AbsoluteSection': AbsoluteSection,
}

// 获取组件
export const getComponent = (componentName: string): React.ComponentType<any> | string => {
  return componentRegistry[componentName] || componentName
}

// 注册新组件
export const registerComponent = (name: string, component: React.ComponentType<any>) => {
  componentRegistry[name] = component
}

// 获取所有已注册的组件名
export const getRegisteredComponents = (): string[] => {
  return Object.keys(componentRegistry)
}

export default componentRegistry

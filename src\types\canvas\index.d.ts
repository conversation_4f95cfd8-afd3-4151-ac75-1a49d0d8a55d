import type { BaseSchema } from "../../schemas";
import type { Undoable } from "../base";

/**
 * 
 * after，子级元素在父级元素的后面
 * before，子级元素在父级元素的前面
 * insertAfter，子级元素在父级元素的后面
 * insertBefore，子级元素在父级元素的前面
 * 
 */
export type LcAppendPosition = 'after' | 'before' | 'insertAfter' | 'insertBefore'
/**
 * LcAppendAbsolutePosition定位时，子组件在父组件里面，位置为'after'的方式追加
 */
export type LcAppendAbsolutePosition = {
  x: number, y: number, type?: 'pixel' | 'percent'
}
export interface ILcCanvas extends Undoable {
  async appendChild(child: BaseSchema): void;
  async appendChildAt(parent: BaseSchema, position: LcAppendPosition | LcAppendAbsolutePosition): void;
  async removeChild(child:BaseSchema):void;
  /**
   * 
   * @param id 
   * @param parent 
   */
  async findChildById(id: string, parent:BaseSchema = null ): Promise<BaseSchema | null>;
  /**
   * 根据鼠标位置找到最近的组件
   * @param x 
   * @param y 
   */
  async findNearestChildByPosition(x:number,y:number):Promise<BaseSchema | null>;

  async updateSchema( schema:BaseSchema, newSchema:BaseSchema  ):Promise<void>;

}
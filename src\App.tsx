import { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import DemoWasm from './demo/demoWasm.tsx'
import Home from './view/home/<USER>'
import Designer  from './view/WorkShop/Designer.tsx';
import GridSection from './ndesignUI/container/GridSection.tsx'
import AbsoluteSection from './ndesignUI/container/AbsoluteSection.tsx'

import './App.scss'

function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      
      <Router>
        <Routes>
          {/* 定义根路径路由，渲染 Home 组件 */}
          <Route path="/" element={<Home />} />
          <Route path="/comp" element={<GridSection />} />
          <Route path="/abs" element={<AbsoluteSection />} />
          <Route path="/dx" element={<Designer />} />
          {/* 定义 about 路径路由，渲染 About 组件 */}
          <Route path="/wasm" element={<DemoWasm />} />
        </Routes>
      </Router>
    </>
  )
}

export default App

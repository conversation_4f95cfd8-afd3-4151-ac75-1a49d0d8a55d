export const createGuid = ()=>{
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const createComponentId = ()=>{
  // TODO
  return Math.floor(Math.random()*1000)+''
}


export function Array2Object(arr: Array<{ name: string; value?: any }>) {
  const res: Record<string, any> = {}
  for (let item of arr) {
    res[item.name] = item.value
  }
  return res
}
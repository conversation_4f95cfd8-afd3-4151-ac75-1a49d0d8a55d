import type { BaseSchema } from "nui";
import { createComponentId, Array2Object } from "../utils";
import type { IRender } from "./IRender";
import React from 'react'
import { getComponent } from "../components/ComponentRegistry"

export default class ReactRender implements IRender{
    
    constructor(){

    }
   
    createElement = (element: BaseSchema) => {
      if( !element.id ){
        element.id = createComponentId();
      }
      const props = { ...Array2Object(element.props || []), 'data-id': element.id }
      // debugger

      let children:any[] = []
      if(element.body){
        const keys = Object.keys(element.body);
        if(keys.length>0){
          children = keys.map(item=>{
            const child = element.body![item]
            return this.createElement(child)
          })
        }
      }
      const comp = getComponent(element.component);
      const ele = React.createElement(comp, props,
        typeof element.body == 'string' ? element.body : children
      );
      return ele;
    }

    async render(schema: BaseSchema ) {
      const ele = this.createElement(schema)
      return ele;
    }

}
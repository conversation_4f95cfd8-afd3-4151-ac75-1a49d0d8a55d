import React, { useRef, useEffect, useState } from 'react'
import { createRoot, type Root } from 'react-dom/client';
import type { SchemaRenderProps } from '../../types';
import { BladeRender } from '../../core/render/BladeRender';
import './index.scss'

export const BladeView = (props: SchemaRenderProps) => {
  const root = useRef(null);
  const documentContent = useRef<Document>(null);
  const [loading, setLoading] = useState(true);
  const reactRootRef = useRef<Root | null>(null);

  const render = async (schema: any) => {
    if (!schema || !schema.template) {
      console.error('不可用的数据')
      return;
    }
    if (root.current) {
      if (!documentContent.current) {
        console.log('渲染组件')
        // 创建 iframe 元素
        const iframe = document.createElement('iframe');
        // 隐藏 iframe 的边框
        iframe.style.border = 'none';

        

        // 将 iframe 添加到 root 容器中
        (root.current as HTMLElement).appendChild(iframe);

        // 等待 iframe 的文档加载完成
        await new Promise<void>((resolve) => {
          iframe.onload = () => {
            resolve();
          };
          // 为 iframe 设置一个空白的 src 以触发加载事件
          iframe.src = 'about:blank';
        });

        // 获取 iframe 的文档对象
        const iframeDocument = iframe.contentDocument;
        
        if (iframeDocument) {
          const link = iframeDocument.createElement('link');
          link.rel = 'stylesheet';
          link.type = 'text/css';
          link.href = "/src/view/render/index.scss";
          iframeDocument.head.appendChild(link);

          // 在 iframe 中创建 root 实例（仅首次）
          if (!reactRootRef.current) {
            console.log('🎬 BladeView 首次渲染，创建 React root');
            reactRootRef.current = createRoot(iframeDocument.body);
          }

          const ele = await (BladeRender.getInstance()).render(schema) as any;
          setLoading(false)
          // 使用缓存的 root 实例渲染组件
          console.log('🔄 BladeView 使用现有 root 渲染');
          reactRootRef.current.render([ele]);

          documentContent.current = iframeDocument;
        }
        
      }else{
        // 热更新渲染
        if (!reactRootRef.current) {
          console.log('🔥 BladeView 热更新，创建 React root');
          reactRootRef.current = createRoot(documentContent.current.body);
        }

        const ele = await (BladeRender.getInstance()).render(schema) as any;
        setLoading(false)
        // 使用缓存的 root 实例渲染组件
        console.log('🔄 BladeView 热更新使用现有 root 渲染');
        reactRootRef.current.render([ele]);

      }

    }
  }



  useEffect(() => {
    const run = async () => {
      if (props.schema) {
        render(props.schema)
      }
    }
    run()
  }, [props.schema])

  return (
    <>
      <div ref={root} className='root'>{loading ? <span>loading schema...</span> : null}</div>
    </>
  )
}
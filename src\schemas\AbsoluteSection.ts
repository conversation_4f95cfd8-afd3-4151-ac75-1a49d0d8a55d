import AbstractSchema, { type BaseSchema } from './index'

export interface AbsoluteSectionProps {
  width?: number | string
  height?: number | string
}

export interface AbsoluteSectionSchema extends BaseSchema {
  compName: 'AbsoluteSection'
  props?: AbsoluteSectionProps
}

export class AbsoluteSection extends AbstractSchema {
  component: string = 'AbsoluteSection'
  name: string = '绝对定位容器'
  reactive = false
  dataId = ''
  class = ''
  style = ''
  props = []
  
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return `<div class='absolute-section' data-id='{{id}}' style='position: relative; width: {{props.width || "100%"}}; height: {{props.height || "400px"}}; border: 2px dashed #ccc; border-radius: 8px; background-color: #f9f9f9;'>
      {{#each body}}
        <div style='position: absolute; left: {{x}}px; top: {{y}}px;'>
          {{{this}}}
        </div>
      {{/each}}
    </div>`
  }
}

export default new AbsoluteSection([
  {
    ref: 'width',
    label: '宽度',
    format: 'string',
    default: '100%',
    bindable: true,
  },
  {
    ref: 'height',
    label: '高度', 
    format: 'string',
    default: '400px',
    bindable: true,
  }
])

import React, { useState } from 'react'
import { FlowSection } from '../ndesignUI/container/FlowSection'
import type { BaseSchema } from 'nui'

const FlowSectionDemo = () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'FlowSection',
    id: 'flow-demo',
    body: {
      'btn1': {
        component: 'button',
        id: 'btn1',
        name: '主要按钮'
      },
      'btn2': {
        component: 'button',
        id: 'btn2',
        name: '次要按钮'
      },
      'text1': {
        component: 'text',
        id: 'text1',
        name: '这是一段文本内容'
      },
      'div1': {
        component: 'div',
        id: 'div1',
        name: '容器块'
      }
    }
  })

  const [direction, setDirection] = useState<'row' | 'column'>('row')
  const [wrap, setWrap] = useState(true)
  const [gap, setGap] = useState(10)
  const [rowGap, setRowGap] = useState(6)
  const [columnGap, setColumnGap] = useState(10)
  const [justify, setJustify] = useState<'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly'>('flex-start')
  const [align, setAlign] = useState<'flex-start' | 'center' | 'flex-end' | 'stretch'>('flex-start')

  const handleSchemaChange = (newSchema: BaseSchema) => {
    setSchema(newSchema)
    console.log('Schema 更新:', newSchema)
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>FlowSection 流式布局容器演示</h2>
      
      {/* 控制面板 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '16px', 
        border: '1px solid #ddd', 
        borderRadius: '8px',
        backgroundColor: '#f8f9fa'
      }}>
        <h3 style={{ margin: '0 0 16px 0' }}>布局控制</h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>方向:</label>
            <select 
              value={direction} 
              onChange={(e) => setDirection(e.target.value as 'row' | 'column')}
              style={{ width: '100%', padding: '4px 8px', border: '1px solid #ccc', borderRadius: '4px' }}
            >
              <option value="row">水平 (row)</option>
              <option value="column">垂直 (column)</option>
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>换行:</label>
            <select 
              value={wrap ? 'wrap' : 'nowrap'} 
              onChange={(e) => setWrap(e.target.value === 'wrap')}
              style={{ width: '100%', padding: '4px 8px', border: '1px solid #ccc', borderRadius: '4px' }}
            >
              <option value="wrap">允许换行</option>
              <option value="nowrap">不换行</option>
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>列间距: {columnGap}px</label>
            <input
              type="range"
              min="0"
              max="50"
              value={columnGap}
              onChange={(e) => setColumnGap(Number(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>行间距: {rowGap}px</label>
            <input
              type="range"
              min="0"
              max="30"
              value={rowGap}
              onChange={(e) => setRowGap(Number(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>主轴对齐:</label>
            <select 
              value={justify} 
              onChange={(e) => setJustify(e.target.value as any)}
              style={{ width: '100%', padding: '4px 8px', border: '1px solid #ccc', borderRadius: '4px' }}
            >
              <option value="flex-start">起始</option>
              <option value="center">居中</option>
              <option value="flex-end">结束</option>
              <option value="space-between">两端对齐</option>
              <option value="space-around">环绕对齐</option>
              <option value="space-evenly">均匀对齐</option>
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontWeight: '500' }}>交叉轴对齐:</label>
            <select 
              value={align} 
              onChange={(e) => setAlign(e.target.value as any)}
              style={{ width: '100%', padding: '4px 8px', border: '1px solid #ccc', borderRadius: '4px' }}
            >
              <option value="flex-start">起始</option>
              <option value="center">居中</option>
              <option value="flex-end">结束</option>
              <option value="stretch">拉伸</option>
            </select>
          </div>
        </div>
      </div>

      {/* FlowSection 演示 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>流式布局容器</h3>
        <p style={{ color: '#666', fontSize: '14px', margin: '8px 0' }}>
          双击容器添加按钮，Shift+双击添加文本，Ctrl+双击添加容器，或从外部拖拽组件到容器中。
          <strong>拖拽组件到其他组件的左侧或右侧可以插入到指定位置。</strong>
        </p>
        
        <FlowSection
          schema={schema}
          width="100%"
          height="300px"
          direction={direction}
          wrap={wrap}
          gap={gap}
          rowGap={rowGap}
          columnGap={columnGap}
          justify={justify}
          align={align}
          onSchemaChange={handleSchemaChange}
        />
      </div>

      {/* Schema 显示 */}
      <div>
        <h3>当前 Schema</h3>
        <pre style={{ 
          background: '#f8f9fa', 
          padding: '16px', 
          borderRadius: '8px', 
          border: '1px solid #ddd',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '300px'
        }}>
          {JSON.stringify(schema, null, 2)}
        </pre>
      </div>

      {/* 使用说明 */}
      <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#e7f3ff', borderRadius: '8px' }}>
        <h4 style={{ margin: '0 0 12px 0', color: '#0066cc' }}>使用说明</h4>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#333' }}>
          <li><strong>双击容器</strong>：添加按钮组件</li>
          <li><strong>Shift + 双击</strong>：添加文本组件</li>
          <li><strong>Ctrl + 双击</strong>：添加 div 容器组件</li>
          <li><strong>拖拽组件</strong>：从模块选择器拖拽组件到容器中</li>
          <li><strong>拖拽插入</strong>：拖拽组件到其他组件的左侧或右侧可以插入到指定位置</li>
          <li><strong>调整参数</strong>：使用上方控制面板调整布局参数</li>
          <li><strong>嵌套布局</strong>：支持在流式布局中嵌套其他流式布局容器</li>
        </ul>
      </div>
    </div>
  )
}

export default FlowSectionDemo

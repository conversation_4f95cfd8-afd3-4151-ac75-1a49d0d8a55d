import React, { useState } from 'react'
import { AbsoluteSection } from '../ndesignUI/container/AbsoluteSection'
import type { BaseSchema } from '../schemas'

const AbsoluteSectionDemo = () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'AbsoluteSection',
    dataId: 'demo-absolute-section',
    name: '演示绝对定位容器',
    body: []
  })

  const handleSchemaChange = (newSchema: BaseSchema) => {
    setSchema(newSchema)
    console.log('Schema updated:', newSchema)
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>AbsoluteSection 演示</h2>
      <p>双击容器添加按钮，Shift+双击添加容器，Ctrl+双击添加嵌套容器</p>
      
      <AbsoluteSection
        schema={schema}
        width="600px"
        height="400px"
        onSchemaChange={handleSchemaChange}
      />
      
      <div style={{ marginTop: '20px' }}>
        <h3>当前 Schema:</h3>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '10px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '200px'
        }}>
          {JSON.stringify(schema, null, 2)}
        </pre>
      </div>
    </div>
  )
}

export default AbsoluteSectionDemo

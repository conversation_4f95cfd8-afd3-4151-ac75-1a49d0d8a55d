import React, {useRef, useEffect } from 'react'
import ReactRender  from '../../../../core/render/ReactRender'
import { createRoot } from 'react-dom/client';


export const ReactView = ( props )=>{
  const root = useRef(null);
  useEffect(()=>{
    const run = async ()=>{
      if( root.current ){
        // 创建 root 实例
        const rootInstance = createRoot(root.current);
        const ele:any = (await new ReactRender().render(props.schema))
        // 使用 root 实例的 render 方法渲染组件
        rootInstance.render([ele]);
        
      }
    }
    debugger
    run()
  })

  return <>
    <div ref={root}>ReactView</div>
  </>

  
}
import { useState } from 'react'
import { LcCanvas } from './component/canvas/LcCanvas'
import SideMenu from './component/sidebar/SideMenu'
import ModuleSelector, { type ModuleItem } from './component/modules/ModuleSelector'
import { AbsoluteSection } from 'lcanvas'
import type { BaseSchema } from 'nui'
import './Designer.scss'

export default () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'div',
    id: '1',
    body: {
      'default': {
        component: 'div',
        id: '2222'
      }
    }
  })

  const [showModuleSelector, setShowModuleSelector] = useState(false)
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const [canvasMode, setCanvasMode] = useState<'lcanvas' | 'absolute'>('lcanvas')

  const handleMenuSelect = (menuId: string, subMenuId?: string) => {
    console.log('选中菜单:', menuId, subMenuId)
    setActiveMenu(subMenuId || menuId)

    // 如果选择的是模块菜单，显示模块选择器
    if (menuId === 'module') {
      setShowModuleSelector(true)
    }
  }

  const handleModuleSelect = (module: ModuleItem) => {
    console.log('选中模块:', module)

    // 创建新的组件实例
    const newComponentId = `${module.component}-${Date.now()}`
    const newComponent: BaseSchema = {
      component: module.component,
      id: newComponentId,
      name: module.name,
      props: module.props || []
    }

    // 更新 schema，添加新组件到 body 中
    setSchema(prevSchema => ({
      ...prevSchema,
      body: {
        ...prevSchema.body,
        [newComponentId]: newComponent
      }
    }))

    setShowModuleSelector(false)
  }

  const handleSchemaChange = (newSchema: BaseSchema) => {
    setSchema(newSchema)
  }

  
  return (
    <div className="designer-container">
      <SideMenu onMenuSelect={handleMenuSelect} />

      <div className="designer-main">
        <div className="canvas-toolbar">
          <div className="canvas-mode-switcher">
            <button
              className={`mode-btn ${canvasMode === 'lcanvas' ? 'active' : ''}`}
              onClick={() => setCanvasMode('lcanvas')}
            >
              LCanvas 模式
            </button>
            <button
              className={`mode-btn ${canvasMode === 'absolute' ? 'active' : ''}`}
              onClick={() => setCanvasMode('absolute')}
            >
              绝对定位模式
            </button>
          </div>
          <div className="canvas-actions">
            <button
              className="action-btn"
              onClick={() => setShowModuleSelector(true)}
            >
              添加组件
            </button>
          </div>
        </div>

        <div className="canvas-area">
          {canvasMode === 'lcanvas' ? (
            <LcCanvas scheme={schema} onSchemaChange={handleSchemaChange} />
          ) : (
            <AbsoluteSection
              schema={schema}
              width="100%"
              height="500px"
              onSchemaChange={handleSchemaChange}
            />
          )}
        </div>

        {activeMenu && (
          <div className="info-panel">
            <h4>当前选中: {activeMenu}</h4>
            <div className="schema-preview">
              <h5>Schema 预览:</h5>
              <pre>{JSON.stringify(schema, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>

      <ModuleSelector
        isVisible={showModuleSelector}
        onModuleSelect={handleModuleSelect}
        onClose={() => setShowModuleSelector(false)}
      />
    </div>
  )
}
import React, { useState } from 'react';
import Tree, { type TreeNode } from './view/WorkShop/component/structTree/Tree';

const DemoTree: React.FC = () => {
  // 示例数据
  const [treeData, setTreeData] = useState<TreeNode[]>([
    {
      id: 1,
      label: '一级 1',
      children: [
        {
          id: 4,
          label: '二级 1-1',
          children: [
            { id: 9, label: '三级 1-1-1' },
            { id: 10, label: '三级 1-1-2' }
          ]
        }
      ]
    },
    {
      id: 2,
      label: '一级 2',
      children: [
        { id: 5, label: '二级 2-1' },
        { id: 6, label: '二级 2-2' }
      ]
    },
    {
      id: 3,
      label: '一级 3',
      children: [
        { id: 7, label: '二级 3-1' },
        { id: 8, label: '二级 3-2' }
      ]
    }
  ]);

  const [searchValue, setSearchValue] = useState('');
  const [checkedKeys, setCheckedKeys] = useState<(string | number)[]>([1, 4]);

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Tree 组件演示</h1>
      
      <div style={{ marginBottom: '30px' }}>
        <h2>基础用法</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            defaultExpandAll={true}
            onNodeClick={(node) => console.log('点击节点:', node)}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>带复选框</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            showCheckbox={true}
            defaultCheckedKeys={checkedKeys}
            onCheck={(checkedKeys, checkedNodes) => {
              console.log('选中的键:', checkedKeys);
              console.log('选中的节点:', checkedNodes);
              setCheckedKeys(checkedKeys);
            }}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>搜索功能</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            showSearch={true}
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            defaultExpandAll={true}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>拖拽排序</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            draggable={true}
            defaultExpandAll={true}
            onNodeDrop={(draggingNode, dropNode, dropType, newData) => {
              console.log('拖拽完成:', { draggingNode, dropNode, dropType });
              setTreeData(newData);
            }}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>手风琴模式</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            accordion={true}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>自定义节点内容</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            renderContent={(node) => (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ 
                  background: '#007bff', 
                  color: 'white', 
                  padding: '2px 6px', 
                  borderRadius: '10px', 
                  fontSize: '10px' 
                }}>
                  {node.id}
                </span>
                <span>{node.label}</span>
                <span style={{ 
                  background: '#28a745', 
                  color: 'white', 
                  padding: '1px 4px', 
                  borderRadius: '2px', 
                  fontSize: '10px' 
                }}>
                  自定义
                </span>
              </div>
            )}
          />
        </div>
      </div>

      <div style={{ marginBottom: '30px' }}>
        <h2>完整功能演示</h2>
        <div style={{ border: '1px solid #ddd', borderRadius: '4px', padding: '16px' }}>
          <Tree
            data={treeData}
            showCheckbox={true}
            showSearch={true}
            draggable={true}
            defaultExpandAll={true}
            defaultCheckedKeys={[1, 4]}
            onNodeClick={(node) => console.log('点击:', node)}
            onCheck={(keys, nodes) => console.log('选中:', keys, nodes)}
            onNodeDrop={(dragging, drop, type, newData) => {
              console.log('拖拽:', { dragging, drop, type });
              setTreeData(newData);
            }}
            renderContent={(node) => (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '16px' }}>
                  {node.children && node.children.length > 0 ? '📁' : '📄'}
                </span>
                <span>{node.label}</span>
                {node.children && (
                  <span style={{ 
                    color: '#666', 
                    fontSize: '12px' 
                  }}>
                    ({node.children.length})
                  </span>
                )}
              </div>
            )}
          />
        </div>
      </div>

      <div style={{ marginTop: '40px', padding: '20px', background: '#f8f9fa', borderRadius: '8px' }}>
        <h3>当前树形数据</h3>
        <pre style={{ 
          background: 'white', 
          padding: '16px', 
          borderRadius: '4px', 
          overflow: 'auto',
          fontSize: '12px',
          lineHeight: '1.4'
        }}>
          {JSON.stringify(treeData, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default DemoTree;

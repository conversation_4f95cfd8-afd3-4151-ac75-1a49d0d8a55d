import { useState, useEffect } from 'react'
import axios from 'axios'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './core/env.ts'

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import DemoWasm from './demo/demoWasm.tsx';
import { getDefinedComponent } from 'nui';

const examleSchema:BaseSchema = {
  component:'div',
  id:'1',
  // template:`<?php 
  // $bladeString = '@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif';
  // #$bladeString = '<div> @customFunction("hello") </div>';
  // $stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
  // $temp = getBladeCode($bladeString);
  // echo "template===$temp";
  
  // echo $stringOutput;
  // ?>`
  template:`@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif`
}
const buttonSchema = {
  component:'div',
  id:'1',
  style:[
    'color:red'
  ],
  template:`
  <ul>
  @for ($i = 1; $i <= count($data["array"]); $i++)
      @if ($i % 2 === 0)
          <li>{{ $i }}</li>
      @else
        <li>{{ $i }}</li>
      @endif
  @endfor
</ul>
  `
}


const absoluteSectionSchema:BaseSchema = {
  component:'AbsoluteSection',
  id:'absolute-section-demo',
  name: '绝对定位容器演示',
  body: [{
     
      component: 'button',
      id: 'btn1',
      name: '按钮1'
    },
     {
      component: 'button',
      id: 'btn2',
      name: '按钮2'
    }
  
]}

const tree:BaseSchema = {
  component:'div',
  id:'absolute-section-demo',
  name: 'div',
  template:'<div data-id="div"><div>child</div><slot></slot></div>',
  body:[
    {
      component: 'button',
      id: 'btn1',
      name: '按钮1',
    },
    {
      component: 'button',
      id: 'btn2',
      name: '按钮2',
    }
  ]
}

const imageSchema: BaseSchema = {
  ...getDefinedComponent('image'),
  id: 'image-demo',
}

function DemoTree() {
  const [scheme, setSchema] = useState({})
  
  useEffect(()=>{
    const initializeBladeRender = async () => {
      try {
        const render = BladeRender.getInstance();
        
        // 确保 WASM 已加载
        if (!BladeRender.isWASMReady) {
          await render.loadWASM();
        }
        
        // 等待所有变量定义完成
        await Promise.all([
          render.defineVariable("data", {
            isAdmin: false,
            array: [1, 2, 3, 4, 5, 6],
            object: { a: 1, b: 2 }
          }),
          await render.defineVariable("props", {
            src: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
            alt: '占位图',
            width: '150px',
            height: 'auto',
            objectFit: 'cover',
            borderRadius: '8px'
          })
        ]);

        // 确保初始化完成后再设置 schema
        setSchema(tree);
        setSchema(imageSchema);
      } catch (error) {
        console.error('BladeRender 初始化失败:', error);
      }
    };
    initializeBladeRender();
  },[])
  return (
    <>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoTree

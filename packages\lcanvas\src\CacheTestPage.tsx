import React, { useState, useEffect } from 'react';
import { useBladeRender } from './hooks/useBladeRender';
import { BladeView } from './view/render/BladeView';
import { CacheDebugPanel } from './components/CacheDebugPanel';
import type { BaseSchema } from 'nui';

const testSchema: BaseSchema = {
  component: 'div',
  id: 'cache-test',
  template: `
    <div style="padding: 20px; border: 2px solid #4CAF50; border-radius: 8px; margin: 10px;">
      <h3>🎉 WASM 缓存测试成功！</h3>
      <p>如果你看到这个消息，说明 WASM 已经成功加载并渲染。</p>
      <p>刷新页面测试缓存效果 - 第二次加载应该会更快！</p>
      <div style="background: #f0f0f0; padding: 10px; border-radius: 4px; margin-top: 10px;">
        <small>当前时间: ${new Date().toLocaleString()}</small>
      </div>
    </div>
  `
};

/**
 * 缓存测试页面
 * 用于验证 WASM 缓存功能是否正常工作
 */
export const CacheTestPage: React.FC = () => {
  const { 
    isReady, 
    isLoading, 
    error, 
    render, 
    ensureReady, 
    getCacheInfo,
    clearCache,
    forceRefresh 
  } = useBladeRender();
  
  const [schema, setSchema] = useState<BaseSchema>({});
  const [loadTime, setLoadTime] = useState<number>(0);
  const [testResults, setTestResults] = useState<string[]>([]);

  // 记录加载时间
  useEffect(() => {
    const startTime = Date.now();
    
    const initTest = async () => {
      try {
        await ensureReady();
        const endTime = Date.now();
        const duration = endTime - startTime;
        setLoadTime(duration);
        
        if (render) {
          await render.defineVariable("testVar", "Hello from Cache Test!");
        }
        
        setSchema(testSchema);
        
        // 记录测试结果
        const cacheInfo = getCacheInfo();
        const result = `加载耗时: ${duration}ms ${cacheInfo?.isValid ? '(从缓存恢复)' : '(首次加载)'}`;
        setTestResults(prev => [result, ...prev.slice(0, 4)]); // 保留最近5次记录
        
      } catch (err) {
        console.error('测试初始化失败:', err);
      }
    };

    initTest();
  }, [ensureReady, render, getCacheInfo]);

  const handleClearCache = () => {
    clearCache();
    setTestResults(prev => ['缓存已清理', ...prev.slice(0, 4)]);
  };

  const handleForceRefresh = async () => {
    const startTime = Date.now();
    try {
      await forceRefresh();
      const endTime = Date.now();
      const duration = endTime - startTime;
      setLoadTime(duration);
      setTestResults(prev => [`强制刷新完成: ${duration}ms`, ...prev.slice(0, 4)]);
    } catch (err) {
      setTestResults(prev => [`强制刷新失败: ${err}`, ...prev.slice(0, 4)]);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <CacheDebugPanel />
      
      <h1>LCanvas WASM 缓存测试页面</h1>
      
      <div style={{ 
        background: '#f8f9fa', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>测试说明</h3>
        <ul>
          <li>首次访问会加载 WASM（3-5秒）</li>
          <li>刷新页面测试缓存效果（应该 &lt;100ms）</li>
          <li>使用右上角调试面板查看缓存状态</li>
          <li>可以手动清理缓存或强制刷新</li>
        </ul>
      </div>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px', 
        marginBottom: '20px' 
      }}>
        <div style={{ 
          background: '#fff', 
          padding: '15px', 
          border: '1px solid #ddd', 
          borderRadius: '8px' 
        }}>
          <h4>当前状态</h4>
          <div>
            <strong>WASM 状态:</strong> 
            <span style={{ 
              color: isReady ? '#4CAF50' : isLoading ? '#FF9800' : '#F44336',
              marginLeft: '8px'
            }}>
              {isReady ? '✅ 已就绪' : isLoading ? '⏳ 加载中' : '❌ 未就绪'}
            </span>
          </div>
          <div><strong>加载时间:</strong> {loadTime}ms</div>
          {error && (
            <div style={{ color: '#F44336', marginTop: '8px' }}>
              <strong>错误:</strong> {error.message}
            </div>
          )}
        </div>

        <div style={{ 
          background: '#fff', 
          padding: '15px', 
          border: '1px solid #ddd', 
          borderRadius: '8px' 
        }}>
          <h4>测试操作</h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                background: '#007bff',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              刷新页面测试缓存
            </button>
            
            <button
              onClick={handleClearCache}
              style={{
                padding: '8px 16px',
                border: '1px solid #dc3545',
                borderRadius: '4px',
                background: '#dc3545',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              清理缓存
            </button>
            
            <button
              onClick={handleForceRefresh}
              style={{
                padding: '8px 16px',
                border: '1px solid #28a745',
                borderRadius: '4px',
                background: '#28a745',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              强制刷新 WASM
            </button>
          </div>
        </div>
      </div>

      <div style={{ 
        background: '#fff', 
        padding: '15px', 
        border: '1px solid #ddd', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h4>测试记录</h4>
        {testResults.length > 0 ? (
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            {testResults.map((result, index) => (
              <li key={index} style={{ marginBottom: '4px' }}>
                <small>{result}</small>
              </li>
            ))}
          </ul>
        ) : (
          <p style={{ margin: 0, color: '#666' }}>暂无测试记录</p>
        )}
      </div>

      {/* WASM 渲染内容 */}
      {isLoading && (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px',
          background: '#f8f9fa',
          borderRadius: '8px'
        }}>
          <div>⏳ 正在加载 WASM...</div>
          <small style={{ color: '#666' }}>首次加载可能需要几秒钟</small>
        </div>
      )}

      {error && (
        <div style={{ 
          padding: '20px',
          background: '#f8d7da',
          border: '1px solid #f5c6cb',
          borderRadius: '8px',
          color: '#721c24'
        }}>
          <h4>❌ 加载失败</h4>
          <p>{error.message}</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '8px 16px',
              border: '1px solid #721c24',
              borderRadius: '4px',
              background: 'transparent',
              color: '#721c24',
              cursor: 'pointer'
            }}
          >
            重新加载
          </button>
        </div>
      )}

      {isReady && schema.template && (
        <BladeView schema={schema} />
      )}
    </div>
  );
};

import React, { useState, useEffect, useRef } from 'react';

export interface TreeNode {
  id: string | number;
  label: string;
  children?: TreeNode[];
  disabled?: boolean;
  [key: string]: any;
}

export interface TreeProps {
  data: TreeNode[];
  defaultExpandAll?: boolean;
  defaultExpandedKeys?: (string | number)[];
  defaultCheckedKeys?: (string | number)[];
  showCheckbox?: boolean;
  accordion?: boolean;
  indent?: number;
  expandOnClickNode?: boolean;
  checkOnClickNode?: boolean;

  // 搜索相关
  showSearch?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  filterNodeMethod?: (value: string, data: TreeNode) => boolean;

  // 拖拽排序相关
  draggable?: boolean;
  allowDrop?: (draggingNode: TreeNode, dropNode: TreeNode, type: 'prev' | 'next' | 'inner') => boolean;
  allowDrag?: (draggingNode: TreeNode) => boolean;

  // 事件回调
  onNodeClick?: (data: TreeNode, node: TreeNode, component: any) => void;
  onCheck?: (checkedKeys: (string | number)[], checkedNodes: TreeNode[]) => void;
  onNodeExpand?: (data: TreeNode, node: TreeNode, component: any) => void;
  onNodeCollapse?: (data: TreeNode, node: TreeNode, component: any) => void;
  onNodeDrop?: (draggingNode: TreeNode, dropNode: TreeNode, dropType: 'prev' | 'next' | 'inner', newData: TreeNode[]) => void;

  // 自定义渲染
  renderContent?: (node: TreeNode) => React.ReactNode;
}

// 获取所有节点的 key
const getAllNodeKeys = (data: TreeNode[]): (string | number)[] => {
  const keys: (string | number)[] = [];
  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach(node => {
      keys.push(node.id);
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(data);
  return keys;
};

// 过滤节点
const filterNodes = (
  nodes: TreeNode[], 
  searchValue: string, 
  filterMethod?: (value: string, data: TreeNode) => boolean
): TreeNode[] => {
  if (!searchValue) return nodes;

  const defaultFilter = (value: string, node: TreeNode) => 
    node.label.toLowerCase().includes(value.toLowerCase());
  
  const filter = filterMethod || defaultFilter;

  const filterNode = (node: TreeNode): TreeNode | null => {
    const isMatch = filter(searchValue, node);
    const filteredChildren = node.children 
      ? node.children.map(filterNode).filter(Boolean) as TreeNode[]
      : [];

    if (isMatch || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren.length > 0 ? filteredChildren : node.children
      };
    }
    return null;
  };

  return nodes.map(filterNode).filter(Boolean) as TreeNode[];
};

// 树节点组件
const TreeNodeComponent: React.FC<{
  node: TreeNode;
  level: number;
  expandedKeys: Set<string | number>;
  checkedKeys: Set<string | number>;
  showCheckbox: boolean;
  indent: number;
  expandOnClickNode: boolean;
  checkOnClickNode: boolean;
  draggable: boolean;
  onToggleExpand: (nodeId: string | number) => void;
  onToggleCheck: (nodeId: string | number, checked: boolean) => void;
  onNodeClick: (node: TreeNode) => void;
  onDragStart?: (node: TreeNode) => void;
  onDragOver?: (e: React.DragEvent, node: TreeNode) => void;
  onDrop?: (e: React.DragEvent, node: TreeNode) => void;
  renderContent?: (node: TreeNode) => React.ReactNode;
}> = ({
  node,
  level,
  expandedKeys,
  checkedKeys,
  showCheckbox,
  indent,
  expandOnClickNode,
  checkOnClickNode,
  draggable,
  onToggleExpand,
  onToggleCheck,
  onNodeClick,
  onDragStart,
  onDragOver,
  onDrop,
  renderContent
}) => {
  const hasChildren = node.children && node.children.length > 0;
  const isExpanded = expandedKeys.has(node.id);
  const isChecked = checkedKeys.has(node.id);

  const handleClick = () => {
    if (expandOnClickNode && hasChildren) {
      onToggleExpand(node.id);
    }
    if (checkOnClickNode && showCheckbox) {
      onToggleCheck(node.id, !isChecked);
    }
    onNodeClick(node);
  };

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleExpand(node.id);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    onToggleCheck(node.id, e.target.checked);
  };

  return (
    <div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          paddingLeft: `${level * indent}px`,
          height: '26px',
          cursor: 'pointer',
          userSelect: 'none'
        }}
        onClick={handleClick}
        draggable={draggable}
        onDragStart={() => onDragStart?.(node)}
        onDragOver={(e) => onDragOver?.(e, node)}
        onDrop={(e) => onDrop?.(e, node)}
      >
        {/* 展开/收起图标 */}
        <span
          style={{
            width: '16px',
            height: '16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '4px',
            fontSize: '12px',
            color: '#c0c4cc',
            cursor: hasChildren ? 'pointer' : 'default'
          }}
          onClick={hasChildren ? handleExpandClick : undefined}
        >
          {hasChildren ? (isExpanded ? '▼' : '▶') : ''}
        </span>

        {/* 复选框 */}
        {showCheckbox && (
          <input
            type="checkbox"
            checked={isChecked}
            onChange={handleCheckboxChange}
            style={{ marginRight: '8px' }}
            onClick={(e) => e.stopPropagation()}
          />
        )}

        {/* 节点内容 */}
        <span style={{ flex: 1, fontSize: '14px', color: '#606266' }}>
          {renderContent ? renderContent(node) : node.label}
        </span>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map(child => (
            <TreeNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
              expandedKeys={expandedKeys}
              checkedKeys={checkedKeys}
              showCheckbox={showCheckbox}
              indent={indent}
              expandOnClickNode={expandOnClickNode}
              checkOnClickNode={checkOnClickNode}
              draggable={draggable}
              onToggleExpand={onToggleExpand}
              onToggleCheck={onToggleCheck}
              onNodeClick={onNodeClick}
              onDragStart={onDragStart}
              onDragOver={onDragOver}
              onDrop={onDrop}
              renderContent={renderContent}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const Tree: React.FC<TreeProps> = ({
  data,
  defaultExpandAll = false,
  defaultExpandedKeys = [],
  defaultCheckedKeys = [],
  showCheckbox = false,
  accordion = false,
  indent = 18,
  expandOnClickNode = true,
  checkOnClickNode = false,
  showSearch = false,
  searchPlaceholder = '请输入关键词进行过滤',
  searchValue = '',
  onSearchChange,
  filterNodeMethod,
  draggable = false,
  allowDrop,
  allowDrag,
  onNodeClick,
  onCheck,
  onNodeExpand,
  onNodeCollapse,
  onNodeDrop,
  renderContent,
}) => {
  // 状态管理
  const [expandedKeys, setExpandedKeys] = useState<Set<string | number>>(
    new Set(defaultExpandAll ? getAllNodeKeys(data) : defaultExpandedKeys)
  );
  const [checkedKeys, setCheckedKeys] = useState<Set<string | number>>(
    new Set(defaultCheckedKeys)
  );
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue);
  const [draggedNode, setDraggedNode] = useState<TreeNode | null>(null);

  // 处理搜索值变化
  useEffect(() => {
    setInternalSearchValue(searchValue);
  }, [searchValue]);

  // 过滤后的数据
  const filteredData = filterNodes(data, internalSearchValue, filterNodeMethod);

  // 切换展开状态
  const handleToggleExpand = (nodeId: string | number) => {
    const newExpandedKeys = new Set(expandedKeys);
    
    if (accordion) {
      // 手风琴模式：只能展开一个同级节点
      newExpandedKeys.clear();
      if (!expandedKeys.has(nodeId)) {
        newExpandedKeys.add(nodeId);
      }
    } else {
      if (expandedKeys.has(nodeId)) {
        newExpandedKeys.delete(nodeId);
      } else {
        newExpandedKeys.add(nodeId);
      }
    }
    
    setExpandedKeys(newExpandedKeys);
  };

  // 切换选中状态
  const handleToggleCheck = (nodeId: string | number, checked: boolean) => {
    const newCheckedKeys = new Set(checkedKeys);
    
    if (checked) {
      newCheckedKeys.add(nodeId);
    } else {
      newCheckedKeys.delete(nodeId);
    }
    
    setCheckedKeys(newCheckedKeys);
    
    // 获取选中的节点数据
    const getCheckedNodes = (nodes: TreeNode[]): TreeNode[] => {
      const result: TreeNode[] = [];
      const traverse = (nodeList: TreeNode[]) => {
        nodeList.forEach(node => {
          if (newCheckedKeys.has(node.id)) {
            result.push(node);
          }
          if (node.children) {
            traverse(node.children);
          }
        });
      };
      traverse(nodes);
      return result;
    };
    
    onCheck?.(Array.from(newCheckedKeys), getCheckedNodes(data));
  };

  // 节点点击
  const handleNodeClick = (node: TreeNode) => {
    onNodeClick?.(node, node, null);
  };

  // 搜索处理
  const handleSearchChange = (value: string) => {
    setInternalSearchValue(value);
    onSearchChange?.(value);
  };

  // 拖拽处理
  const handleDragStart = (node: TreeNode) => {
    setDraggedNode(node);
  };

  const handleDragOver = (e: React.DragEvent, node: TreeNode) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropNode: TreeNode) => {
    e.preventDefault();
    if (!draggedNode || draggedNode.id === dropNode.id) return;

    // 这里简化处理，实际应该根据拖拽位置判断插入类型
    onNodeDrop?.(draggedNode, dropNode, 'inner', data);
    setDraggedNode(null);
  };

  return (
    <div style={{ fontSize: '14px', color: '#606266' }}>
      {/* 搜索框 */}
      {showSearch && (
        <div style={{ marginBottom: '8px' }}>
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={internalSearchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #dcdfe6',
              borderRadius: '4px',
              fontSize: '14px',
              outline: 'none'
            }}
          />
        </div>
      )}

      {/* 树形结构 */}
      <div>
        {filteredData.map(node => (
          <TreeNodeComponent
            key={node.id}
            node={node}
            level={0}
            expandedKeys={expandedKeys}
            checkedKeys={checkedKeys}
            showCheckbox={showCheckbox}
            indent={indent}
            expandOnClickNode={expandOnClickNode}
            checkOnClickNode={checkOnClickNode}
            draggable={draggable}
            onToggleExpand={handleToggleExpand}
            onToggleCheck={handleToggleCheck}
            onNodeClick={handleNodeClick}
            onDragStart={handleDragStart}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            renderContent={renderContent}
          />
        ))}
      </div>
    </div>
  );
};

export default Tree;

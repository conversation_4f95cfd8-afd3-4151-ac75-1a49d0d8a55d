import { makeObservable, observable, action } from 'mobx';
// 定义用户信息的类型
interface UserInfo {
  id?: number;
  name?: string;
  email?: string;
  // 可根据实际需求添加更多字段
}


class UserStore {
  // 存储用户信息，初始化为 null
  @observable userInfo: UserInfo | null = null;

  constructor() {
    // 使当前类的属性和方法具有 MobX 响应式特性
    makeObservable(this);
  }

  /**
   * 模拟用户登录操作，更新用户信息
   * @param userInfo 用户信息对象
   */
  @action
  login(userInfo: UserInfo) {
    this.userInfo = userInfo;
  }

  /**
   * 更新用户信息
   * @param newInfo 新的用户信息对象
   */
  @action
  updateUserInfo(newInfo: Partial<UserInfo>) {
    if (this.userInfo) {
      this.userInfo = { ...this.userInfo, ...newInfo };
    }
  }

  /**
   * 模拟用户登出操作，清空用户信息
   */
  @action
  logout() {
    this.userInfo = null;
  }
}

// 创建 UserStore 实例
const userStore = new UserStore();

export default userStore;

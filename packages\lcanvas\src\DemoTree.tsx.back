import { useState, useEffect, Component } from 'react'
import axios from 'axios'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './core/env.ts'

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import DemoWasm from './demo/demoWasm.tsx';

const examleSchema:BaseSchema = {
  component:'div',
  id:'1',
  // template:`<?php 
  // $bladeString = '@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif';
  // #$bladeString = '<div> @customFunction("hello") </div>';
  // $stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
  // $temp = getBladeCode($bladeString);
  // echo "template===$temp";
  
  // echo $stringOutput;
  // ?>`
  template:`@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif`
}
const buttonSchema = {
  component:'div',
  id:'1',
  style:[
    'color:red'
  ],
  template:`
  <ul>
  @for ($i = 1; $i <= count($data["array"]); $i++)
      @if ($i % 2 === 0)
          <li>{{ $i }}</li>
      @else
        <li>{{ $i }}</li>
      @endif
  @endfor
</ul>
  `
}


const absoluteSectionSchema:BaseSchema = {
  component:'AbsoluteSection',
  id:'absolute-section-demo',
  name: '绝对定位容器演示',
  body: [{
     
      component: 'button',
      id: 'btn1',
      name: '按钮1'

    },
     {
      component: 'button',
      id: 'btn2',
      name: '按钮2'
    }
  
]}

page{

   compoeent[
    'compa',
    'button'
   ]
   
   tree:{
    'div', $ref:
   }
}
// 
循环体，静态容器支持（子元素数据绑定问题。$scope, pageValue）
样式，（多平台支持）（4套（三端？，默认）
属性（配置数据作用域）
模板变量问题，作用域。
组件升级（版本），自定义样式在新组件上应用
组件复用。i18n等
//

const tree:BaseSchema = {
  component:'button',
  id:'absolute-section-demo',
  type' container/form','co'
  name: 'div',
  
  template:'<div data-{id}><div>{{dataId}} $data $datasource["A__1"] {{aaa}} $['data']['aaa'] , $['session'], prop.a, style,  <div data-id="div"><div>child</div><slot name></slot></div>',
  model:['']
  i18n:{

  }
   //button.schema
  body:[
    {
      component: 'button',
      id: 'btn1',
      name: '按钮1',
      props:{
        name:{
          value:'label',
          varibale:'我是一个安氟 '， "$scope $page"
        }
      },
      i18n:{
        "en":{
          "key_A"
        },
        "jp":
          "key_A"
        
      }
      template:'<button>112</button>',
      body:[
        component:'button',
        {

        }
      ]
    },
    {
      component: 'button',
      id: 'btn2',
      name: '按钮2',
    }
  ]
}
function DemoTree() {
  const [scheme, setSchema] = useState({})
  
  useEffect(()=>{
    (async function(){
      let render = BladeRender.getInstance();
      
      await render.defineVariable("isAdmin","0")
      debugger
      // await render.defineVariable("object",'{"a":1,"b":2}')
      // await render.defineVariable("array",[{"a":1,"b":2},{"a":1,"b":2}])


      // todo
      //await render.definedServiceVariable("array",{api:'',params})
      
     // setSchema(examleSchema);
      setSchema(tree);
    })()
  },[])
  

  
  return (
    <>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoTree

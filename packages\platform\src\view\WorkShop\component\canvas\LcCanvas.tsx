import React,{ useRef, useEffect } from 'react'
import { ReactView } from './ReactView'
import type { BaseSchema } from '../../../../schemas'
import './index.scss'

export type LcCanvasProps = {
  scheme: BaseSchema
  onSchemaChange?: (schema: BaseSchema) => void
}

export const LcCanvas = (props: LcCanvasProps) => {
  const { scheme, onSchemaChange } = props
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    try {
      const moduleData = JSON.parse(e.dataTransfer.getData('application/json'))
      console.log('拖拽的模块:', moduleData)

      // 创建新的组件实例
      const newComponentId = `${moduleData.component}-${Date.now()}`
      const newComponent: BaseSchema = {
        component: moduleData.component,
        id: newComponentId,
        name: moduleData.name
      }

      // 更新 schema
      const updatedSchema = {
        ...scheme,
        body: {
          ...scheme.body,
          [newComponentId]: newComponent
        }
      }

      onSchemaChange?.(updatedSchema)
    } catch (error) {
      console.error('处理拖拽数据失败:', error)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'copy'
  }

  
  return (
    <div
      className="lc-canvas"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      <ReactView schema={scheme} />
    </div>
  )
}
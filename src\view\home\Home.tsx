import React, {useState} from 'react'
import { BladeView } from 'lcanvas'
import type { BaseSchema } from '../../schemas'

const examleSchema:BaseSchema = {
  component:'div',
  dataId:'1',
  template:`<?php $bladeString = '@if($isAdmin) <p>管理员</p> @else <p>普通用户</p> @endif';
  $stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
  echo $stringOutput; ?>`
}


export default ()=>{
  
  const [schema, setSchema] = useState(examleSchema)
  return <BladeView schema={schema}>Home</BladeView>
}
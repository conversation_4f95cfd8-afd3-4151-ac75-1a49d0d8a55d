import { useState, useEffect } from 'react'
import { BladeView, useBladeRender } from 'lcanvas'
import type { BaseSchema } from 'nui'

const testSchema: BaseSchema = {
  component: 'div',
  id: '1',
  template: `<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; background: #f8f9fa; margin: 20px;">
    <h2 style="color: #007bff; margin-bottom: 20px;">🧪 PHP Blade 语法测试</h2>
    
    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h3>⏰ 时间测试</h3>
      <p><strong>当前时间:</strong> {{ date('Y-m-d H:i:s') }}</p>
      <p><strong>格式化时间:</strong> {{ date('F j, Y, g:i a') }}</p>
    </div>

    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h3>🔐 权限测试</h3>
      <p><strong>isAdmin 状态:</strong> {{ $data['isAdmin'] ? '是管理员' : '不是管理员' }}</p>
      @if($data['isAdmin'])
        <div style="color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;">
          ✅ <strong>管理员权限：</strong>您可以看到这个内容
        </div>
      @else
        <div style="color: #721c24; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;">
          ❌ <strong>普通用户：</strong>管理员内容已隐藏
        </div>
      @endif
    </div>

    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h3>📊 变量测试</h3>
      <p><strong>标题:</strong> {{ $data['title'] ?? '无标题' }}</p>
      <p><strong>用户名:</strong> {{ $data['user']['name'] ?? '未知用户' }}</p>
      <p><strong>用户角色:</strong> {{ $data['user']['role'] ?? '无角色' }}</p>
    </div>

    <div style="background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
      <h3>🔄 循环测试</h3>
      @if(isset($data['items']) && is_array($data['items']))
        <ul style="margin: 0; padding-left: 20px;">
        @foreach($data['items'] as $item)
          <li style="margin: 5px 0;">{{ $item }}</li>
        @endforeach
        </ul>
      @else
        <p style="color: #666;">没有项目数据</p>
      @endif
    </div>

    <div style="background: #e9ecef; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px;">
      <h3 style="margin-top: 0;">🐛 调试信息</h3>
      <pre style="margin: 0; white-space: pre-wrap;">{{ json_encode($data, JSON_PRETTY_PRINT) }}</pre>
    </div>

    <slot></slot>
  </div>`,
  body:[
    {
      component:'div',
      id:'2',
      template:`<div style="padding: 10px; margin: 10px; border: 1px solid #ddd; border-radius: 4px; background: white;">
        <h4 style="color: #6c757d;">🧩 子组件测试</h4>
        <p>这是一个子组件</p>
        <p><strong>子组件变量:</strong> {{ $data["content"] ?? "无内容" }}</p>
        <slot></slot>
      </div>`,
      body:[
        {
          component:"div",
          id:'3',
          template:`<div style="padding: 5px; background: #e9ecef; border-radius: 3px; margin: 5px 0;">
            <strong>🔗 嵌套组件:</strong> {{ "Hello from nested component!" }}
            <br>
            <small>嵌套级别: 第三层</small>
          </div>`
        }
      ]
    }
  ]
}

function BladeTest() {
  const [schema, setSchema] = useState<BaseSchema>({})
  const { isReady, isLoading, error, ensureReady, render } = useBladeRender()

  useEffect(() => {
    const initializeTest = async () => {
      try {
        console.log('🚀 开始初始化 Blade 测试...')
        
        // 确保 WASM 准备就绪
        await ensureReady()
        
        if (render) {
          console.log('📝 定义测试变量...')
          
          // 定义测试变量
          await render.defineVariable("isAdmin", "1") // 设置为管理员
          await render.defineVariable("data", {
            title: "Platform Blade 测试页面",
            content: "这是通过 BladeView 渲染的内容",
            isAdmin: true,
            user: {
              name: "测试管理员",
              role: "admin"
            },
            items: [
              "第一个项目",
              "第二个项目", 
              "第三个项目"
            ]
          })
          
          console.log('✅ 变量定义完成，设置 schema...')
          setSchema(testSchema)
        }
      } catch (err) {
        console.error('❌ Blade 测试初始化失败:', err)
      }
    }

    initializeTest()
  }, [ensureReady, render])

  if (isLoading) {
    return (
      <div style={{
        padding: '40px',
        textAlign: 'center',
        fontSize: '18px',
        color: '#666'
      }}>
        🔄 正在加载 WASM 和 PHP 引擎...
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        padding: '40px',
        textAlign: 'center',
        color: '#dc3545',
        background: '#f8d7da',
        border: '1px solid #f5c6cb',
        borderRadius: '8px',
        margin: '20px'
      }}>
        <h2>❌ 加载失败</h2>
        <p>{error.message}</p>
        <button 
          onClick={() => window.location.reload()}
          style={{
            padding: '10px 20px',
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔄 重新加载
        </button>
      </div>
    )
  }

  if (!isReady) {
    return (
      <div style={{
        padding: '40px',
        textAlign: 'center',
        color: '#856404',
        background: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '8px',
        margin: '20px'
      }}>
        <h2>⏳ 准备中...</h2>
        <p>WASM 引擎正在准备，请稍候...</p>
      </div>
    )
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: '#f5f5f5',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <h1 style={{
          textAlign: 'center',
          color: '#333',
          marginBottom: '30px'
        }}>
          🧪 Platform Blade 语法测试
        </h1>
        
        <BladeView schema={schema} />
        
        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'white',
          borderRadius: '8px',
          border: '1px solid #ddd'
        }}>
          <h3>📋 测试说明</h3>
          <ul style={{ lineHeight: '1.6' }}>
            <li>✅ 时间函数测试：<code>date()</code></li>
            <li>✅ 条件语句测试：<code>@if/@else/@endif</code></li>
            <li>✅ 变量输出测试：<code>{'{{ $data[\'key\'] }}'}</code></li>
            <li>✅ 循环语句测试：<code>@foreach/@endforeach</code></li>
            <li>✅ JSON 输出测试：<code>json_encode()</code></li>
            <li>✅ 嵌套组件测试：多层 slot 渲染</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default BladeTest

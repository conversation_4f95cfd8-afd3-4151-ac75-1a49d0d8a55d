# Element UI Tree 树形控件

一个模仿 Element UI 的 React Tree 树形控件组件，支持多种功能和自定义选项。

## 功能特性

- ✅ 基础树形结构展示
- ✅ 节点展开/收起
- ✅ 复选框选择
- ✅ **搜索过滤功能**
- ✅ **拖拽排序功能**
- ✅ 自定义节点内容
- ✅ 手风琴模式
- ✅ 自定义缩进
- ✅ 默认展开所有节点
- ✅ 默认选中节点
- ✅ 节点点击事件
- ✅ 复选框状态变化事件
- ✅ Element UI 风格样式

## 快速开始

```tsx
import React, { useState } from 'react';
import Tree, { TreeNode } from './Tree';

// 基础用法
<Tree data={data} onNodeClick={(node) => console.log(node)} />

// 复选框
<Tree data={data} showCheckbox={true} defaultCheckedKeys={[1, 2]} />

// 搜索功能
<Tree data={data} showSearch={true} searchPlaceholder="搜索节点..." />

// 拖拽排序
const [treeData, setTreeData] = useState(data);
<Tree
  data={treeData}
  draggable={true}
  onNodeDrop={(dragging, drop, type, newData) => setTreeData(newData)}
/>
```

## 完整示例

```tsx
import React, { useState } from 'react';
import Tree, { TreeNode } from './Tree';

const data: TreeNode[] = [
  {
    id: '1',
    label: '前端技术',
    children: [
      { id: '1-1', label: 'JavaScript' },
      { id: '1-2', label: 'TypeScript' },
      { id: '1-3', label: 'React' }
    ]
  },
  {
    id: '2',
    label: '后端技术',
    children: [
      { id: '2-1', label: 'Node.js' },
      { id: '2-2', label: 'Python' }
    ]
  }
];

const TreeExample = () => {
  const [treeData, setTreeData] = useState(data);

  return (
    <Tree
      data={treeData}
      defaultExpandAll={true}
      showCheckbox={true}
      showSearch={true}
      draggable={true}
      searchPlaceholder="搜索技术栈..."
      onNodeClick={(node) => console.log('点击:', node.label)}
      onNodeDrop={(dragging, drop, type, newData) => setTreeData(newData)}
      allowDrop={(dragging, drop, type) => dragging.id !== drop.id}
    />
  );
};
```

## API 参考

### 主要属性

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `data` | 树形数据 | `TreeNode[]` | `[]` |
| `defaultExpandAll` | 默认展开所有节点 | `boolean` | `false` |
| `showCheckbox` | 显示复选框 | `boolean` | `false` |
| `showSearch` | 显示搜索框 | `boolean` | `false` |
| `draggable` | 启用拖拽排序 | `boolean` | `false` |
| `accordion` | 手风琴模式 | `boolean` | `false` |
| `indent` | 缩进像素 | `number` | `18` |

### 搜索相关

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `searchPlaceholder` | 搜索框占位符 | `string` | `'输入关键字进行过滤'` |
| `searchValue` | 搜索值（受控） | `string` | `''` |
| `filterNodeMethod` | 自定义过滤方法 | `(value: string, node: TreeNode) => boolean` | - |
| `onSearchChange` | 搜索变化回调 | `(value: string) => void` | - |

### 拖拽相关

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| `allowDrag` | 节点是否可拖拽 | `(node: TreeNode) => boolean` | - |
| `allowDrop` | 是否允许放置 | `(dragging: TreeNode, drop: TreeNode, type: 'prev'\|'next'\|'inner') => boolean` | - |
| `onNodeDrop` | 拖拽完成回调 | `(dragging: TreeNode, drop: TreeNode, type: string, data: TreeNode[]) => void` | - |

### 事件回调

| 事件 | 说明 | 参数 |
|------|------|------|
| `onNodeClick` | 节点点击 | `(node: TreeNode) => void` |
| `onCheck` | 复选框变化 | `(node: TreeNode, checked: boolean) => void` |

### TreeNode 数据结构

```typescript
interface TreeNode {
  id: string | number;        // 唯一标识
  label: string;              // 显示文本
  children?: TreeNode[];      // 子节点
  disabled?: boolean;         // 是否禁用
  [key: string]: any;         // 其他自定义属性
}
```

## 使用说明

- 确保每个节点的 `id` 唯一
- 拖拽功能需要现代浏览器支持
- 拖拽完成后需要在 `onNodeDrop` 中更新数据
- 组件需要 React 16.8+ 和 SCSS 支持

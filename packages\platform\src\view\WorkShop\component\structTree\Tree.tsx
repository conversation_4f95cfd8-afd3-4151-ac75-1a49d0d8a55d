import React, { useState, useCallback, useEffect } from 'react';
import './Tree.scss';

// ==================== 类型定义 ====================

export interface TreeNode {
  id: string | number;
  label: string;
  children?: TreeNode[];
  disabled?: boolean;
  [key: string]: any;
}

export interface TreeProps {
  data: TreeNode[];
  defaultExpandAll?: boolean;
  defaultExpandedKeys?: (string | number)[];
  defaultCheckedKeys?: (string | number)[];
  showCheckbox?: boolean;
  accordion?: boolean;
  indent?: number;
  expandOnClickNode?: boolean;
  checkOnClickNode?: boolean;

  // 搜索相关
  showSearch?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  filterNodeMethod?: (value: string, data: TreeNode) => boolean;

  // 拖拽排序相关
  draggable?: boolean;
  allowDrop?: (draggingNode: TreeNode, dropNode: TreeNode, type: 'prev' | 'next' | 'inner') => boolean;
  allowDrag?: (draggingNode: TreeNode) => boolean;

  // 事件回调
  onNodeClick?: (data: TreeNode) => void;
  onCheck?: (data: TreeNode, checkedInfo: { checkedKeys: (string | number)[] }) => void;
  onNodeExpand?: (data: TreeNode) => void;
  onNodeCollapse?: (data: TreeNode) => void;
  onNodeDrop?: (draggingNode: TreeNode, dropNode: TreeNode, dropType: 'prev' | 'next' | 'inner', data: TreeNode[]) => void;

  // 自定义渲染
  renderContent?: (node: TreeNode) => React.ReactNode;
}

interface TreeNodeProps {
  node: TreeNode;
  level: number;
  expandedKeys: Set<string | number>;
  checkedKeys: Set<string | number>;
  showCheckbox: boolean;
  indent: number;
  draggable: boolean;
  onToggle: (nodeId: string | number) => void;
  onCheck: (nodeId: string | number, checked: boolean) => void;
  onNodeClick: (node: TreeNode) => void;
  onDragStart: (node: TreeNode, e: React.DragEvent) => void;
  onDragOver: (node: TreeNode, e: React.DragEvent) => void;
  onDragLeave: (node: TreeNode, e: React.DragEvent) => void;
  onDrop: (node: TreeNode, e: React.DragEvent) => void;
  renderContent?: (node: TreeNode) => React.ReactNode;
}

// ==================== 工具函数 ====================

const getAllNodeKeys = (nodes: TreeNode[]): (string | number)[] => {
  const keys: (string | number)[] = [];
  const traverse = (nodeList: TreeNode[]) => {
    nodeList.forEach(node => {
      keys.push(node.id);
      if (node.children) traverse(node.children);
    });
  };
  traverse(nodes);
  return keys;
};

const filterTreeData = (
  nodes: TreeNode[],
  searchValue: string,
  filterMethod?: (value: string, data: TreeNode) => boolean
): TreeNode[] => {
  if (!searchValue) return nodes;

  const filter = filterMethod || ((value: string, node: TreeNode) =>
    node.label.toLowerCase().includes(value.toLowerCase())
  );

  const filterNode = (node: TreeNode): TreeNode | null => {
    const matches = filter(searchValue, node);
    const filteredChildren = node.children?.map(filterNode).filter(Boolean) as TreeNode[] || [];

    if (matches || filteredChildren.length > 0) {
      return { ...node, children: filteredChildren };
    }
    return null;
  };

  return nodes.map(filterNode).filter(Boolean) as TreeNode[];
};

const getExpandedKeysForSearch = (nodes: TreeNode[], searchValue: string): Set<string | number> => {
  const expandedKeys = new Set<string | number>();

  const traverse = (nodeList: TreeNode[]) => {
    nodeList.forEach(node => {
      if (node.children && node.children.length > 0) {
        expandedKeys.add(node.id);
        traverse(node.children);
      }
    });
  };

  if (searchValue) {
    traverse(nodes);
  }

  return expandedKeys;
};

// ==================== 拖拽工具函数 ====================

/**
 * 根据ID查找节点
 */
const findNodeById = (nodes: TreeNode[], id: string | number): TreeNode | null => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 深度克隆树数据
 */
const cloneTreeData = (nodes: TreeNode[]): TreeNode[] => {
  return nodes.map(node => ({
    ...node,
    children: node.children ? cloneTreeData(node.children) : undefined
  }));
};

/**
 * 从树中移除节点
 */
const removeNodeFromTree = (nodes: TreeNode[], nodeId: string | number): TreeNode[] => {
  return nodes.filter(node => {
    if (node.id === nodeId) {
      return false;
    }
    if (node.children) {
      node.children = removeNodeFromTree(node.children, nodeId);
    }
    return true;
  });
};

/**
 * 在指定位置插入节点
 */
const insertNodeToTree = (
  nodes: TreeNode[],
  targetId: string | number,
  newNode: TreeNode,
  position: 'prev' | 'next' | 'inner'
): TreeNode[] => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];

    if (node.id === targetId) {
      const newNodes = [...nodes];
      if (position === 'prev') {
        newNodes.splice(i, 0, newNode);
      } else if (position === 'next') {
        newNodes.splice(i + 1, 0, newNode);
      } else if (position === 'inner') {
        newNodes[i] = {
          ...node,
          children: [...(node.children || []), newNode]
        };
      }
      return newNodes;
    }

    if (node.children) {
      const updatedChildren = insertNodeToTree(node.children, targetId, newNode, position);
      if (updatedChildren !== node.children) {
        const newNodes = [...nodes];
        newNodes[i] = { ...node, children: updatedChildren };
        return newNodes;
      }
    }
  }

  return nodes;
};



// ==================== 组件实现 ====================

const TreeNodeComponent: React.FC<TreeNodeProps> = ({
  node,
  level,
  expandedKeys,
  checkedKeys,
  showCheckbox,
  indent,
  draggable,
  onToggle,
  onCheck,
  onNodeClick,
  onDragStart,
  onDragOver,
  onDragLeave,
  onDrop,
  renderContent,
}) => {
  const hasChildren = node.children && node.children.length > 0;
  const expanded = expandedKeys.has(node.id);
  const checked = checkedKeys.has(node.id);

  const handleToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasChildren) onToggle(node.id);
  }, [hasChildren, node.id, onToggle]);

  const handleCheck = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    onCheck(node.id, e.target.checked);
  }, [node.id, onCheck]);

  const handleNodeClick = useCallback(() => {
    onNodeClick(node);
  }, [node, onNodeClick]);

  return (
    <div className="el-tree-node">
      <div
        className={`el-tree-node__content ${node.disabled ? 'is-disabled' : ''}`}
        style={{ paddingLeft: `${level * indent}px` }}
        onClick={handleNodeClick}
        draggable={draggable && !node.disabled}
        onDragStart={(e) => draggable && onDragStart(node, e)}
        onDragOver={(e) => draggable && onDragOver(node, e)}
        onDragLeave={(e) => draggable && onDragLeave(node, e)}
        onDrop={(e) => draggable && onDrop(node, e)}
      >
        {/* 展开/收起图标 */}
        <span
          className={`el-tree-node__expand-icon ${
            hasChildren
              ? expanded
                ? 'el-icon-caret-bottom'
                : 'el-icon-caret-right'
              : 'is-leaf'
          }`}
          onClick={handleToggle}
        >
          {hasChildren && (expanded ? '▶' : '▶')}
        </span>

        {/* 复选框 */}
        {showCheckbox && (
          <label className="el-checkbox">
            <span className={`el-checkbox__input ${checked ? 'is-checked' : ''}`}>
              <span className="el-checkbox__inner" />
              <input
                type="checkbox"
                className="el-checkbox__original"
                checked={checked}
                onChange={handleCheck}
                disabled={node.disabled}
              />
            </span>
          </label>
        )}

        {/* 节点内容 */}
        <span className="el-tree-node__label">
          {renderContent ? renderContent(node) : node.label}
        </span>
      </div>

      {/* 子节点 */}
      {hasChildren && expanded && (
        <div className="el-tree-node__children">
          {node.children!.map((child) => (
            <TreeNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
              expandedKeys={expandedKeys}
              checkedKeys={checkedKeys}
              showCheckbox={showCheckbox}
              indent={indent}
              draggable={draggable}
              onToggle={onToggle}
              onCheck={onCheck}
              onNodeClick={onNodeClick}
              onDragStart={onDragStart}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              onDrop={onDrop}
              renderContent={renderContent}
            />
          ))}
        </div>
      )}
    </div>
  );
};



// 搜索框组件
const SearchInput: React.FC<{
  value: string;
  placeholder: string;
  onChange: (value: string) => void;
}> = ({ value, placeholder, onChange }) => (
  <div className="el-tree__search">
    <input
      type="text"
      className="el-input__inner"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  </div>
);

// ==================== 主组件 ====================

const Tree: React.FC<TreeProps> = ({
  data,
  defaultExpandAll = false,
  defaultExpandedKeys = [],
  defaultCheckedKeys = [],
  showCheckbox = false,
  accordion = false,
  indent = 18,
  expandOnClickNode = true,
  checkOnClickNode = false,
  showSearch = false,
  searchPlaceholder = '请输入关键词进行过滤',
  searchValue = '',
  onSearchChange,
  filterNodeMethod,
  draggable = false,
  allowDrop,
  allowDrag,
  onNodeClick,
  onCheck,
  onNodeExpand,
  onNodeCollapse,
  onNodeDrop,
  renderContent,
}) => {
  // 状态管理
  const [expandedKeys, setExpandedKeys] = useState<Set<string | number>>(
    new Set(defaultExpandAll ? getAllNodeKeys(data) : defaultExpandedKeys)
  );
  const [checkedKeys, setCheckedKeys] = useState<Set<string | number>>(
    new Set(defaultCheckedKeys)
  );
  const [internalSearchValue, setInternalSearchValue] = useState<string>(searchValue);
  const [filteredData, setFilteredData] = useState<TreeNode[]>(data);

  // 拖拽状态管理
  const [dragNode, setDragNode] = useState<TreeNode | null>(null);
  const [dropPosition, setDropPosition] = useState<'prev' | 'next' | 'inner' | null>(null);
  const [treeData, setTreeData] = useState<TreeNode[]>(data);

  // 同步数据变化
  useEffect(() => {
    setTreeData(data);
  }, [data]);

  // 搜索逻辑
  useEffect(() => {
    const filtered = filterTreeData(treeData, internalSearchValue, filterNodeMethod);
    setFilteredData(filtered);

    if (internalSearchValue) {
      const searchExpandedKeys = getExpandedKeysForSearch(filtered, internalSearchValue);
      setExpandedKeys(searchExpandedKeys);
    }
  }, [treeData, internalSearchValue, filterNodeMethod]);

  // 处理搜索变化
  const handleSearchChange = useCallback((value: string) => {
    setInternalSearchValue(value);
    onSearchChange?.(value);
  }, [onSearchChange]);

  // 处理节点展开/收起
  const handleToggle = useCallback((nodeId: string | number) => {
    setExpandedKeys(prev => {
      const newKeys = new Set(prev);
      const isExpanded = newKeys.has(nodeId);

      if (isExpanded) {
        newKeys.delete(nodeId);
        const node = data.find(n => n.id === nodeId);
        if (node) onNodeCollapse?.(node);
      } else {
        newKeys.add(nodeId);
        const node = data.find(n => n.id === nodeId);
        if (node) onNodeExpand?.(node);
      }

      return newKeys;
    });
  }, [data, onNodeExpand, onNodeCollapse]);

  // 处理复选框变化
  const handleCheck = useCallback((nodeId: string | number, checked: boolean) => {
    setCheckedKeys(prev => {
      const newKeys = new Set(prev);
      if (checked) {
        newKeys.add(nodeId);
      } else {
        newKeys.delete(nodeId);
      }

      const node = data.find(n => n.id === nodeId);
      if (node && onCheck) {
        onCheck(node, { checkedKeys: Array.from(newKeys) });
      }

      return newKeys;
    });
  }, [data, onCheck]);

  // 处理节点点击
  const handleNodeClick = useCallback((node: TreeNode) => {
    if (expandOnClickNode && node.children) {
      handleToggle(node.id);
    }
    if (checkOnClickNode && showCheckbox) {
      handleCheck(node.id, !checkedKeys.has(node.id));
    }
    onNodeClick?.(node);
  }, [expandOnClickNode, checkOnClickNode, showCheckbox, checkedKeys, handleToggle, handleCheck, onNodeClick]);

  // 拖拽处理函数
  const handleDragStart = useCallback((node: TreeNode, e: React.DragEvent) => {
    if (!draggable || (allowDrag && !allowDrag(node))) {
      e.preventDefault();
      return;
    }
    setDragNode(node);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', node.id.toString());
  }, [draggable, allowDrag]);

  const handleDragOver = useCallback((node: TreeNode, e: React.DragEvent) => {
    if (!draggable || !dragNode || dragNode.id === node.id) {
      return;
    }

    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    // 计算拖拽位置
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: 'prev' | 'next' | 'inner' = 'inner';
    if (y < height * 0.25) {
      position = 'prev';
    } else if (y > height * 0.75) {
      position = 'next';
    }

    // 检查是否允许放置
    if (allowDrop && !allowDrop(dragNode, node, position)) {
      return;
    }

    setDropPosition(position);
  }, [draggable, dragNode, allowDrop]);

  const handleDragLeave = useCallback((node: TreeNode, e: React.DragEvent) => {
    if (!draggable) return;
    setDropPosition(null);
  }, [draggable]);

  const handleDrop = useCallback((node: TreeNode, e: React.DragEvent) => {
    if (!draggable || !dragNode || !dropPosition) {
      return;
    }

    e.preventDefault();

    // 检查是否允许放置
    if (allowDrop && !allowDrop(dragNode, node, dropPosition)) {
      return;
    }

    // 执行拖拽操作
    const newTreeData = cloneTreeData(treeData);
    const updatedData = removeNodeFromTree(newTreeData, dragNode.id);
    const finalData = insertNodeToTree(updatedData, node.id, dragNode, dropPosition);

    setTreeData(finalData);
    setFilteredData(finalData);

    // 触发回调
    onNodeDrop?.(dragNode, node, dropPosition, finalData);

    // 清理状态
    setDragNode(null);
    setDropPosition(null);
  }, [draggable, dragNode, dropPosition, allowDrop, treeData, onNodeDrop]);

  return (
    <div className="el-tree">
      {showSearch && (
        <SearchInput
          value={internalSearchValue}
          placeholder={searchPlaceholder}
          onChange={handleSearchChange}
        />
      )}

      <div className="el-tree__content">
        {filteredData.length > 0 ? (
          filteredData.map((node) => (
            <TreeNodeComponent
              key={node.id}
              node={node}
              level={0}
              expandedKeys={expandedKeys}
              checkedKeys={checkedKeys}
              showCheckbox={showCheckbox}
              indent={indent}
              draggable={draggable}
              onToggle={handleToggle}
              onCheck={handleCheck}
              onNodeClick={handleNodeClick}
              onDragStart={handleDragStart}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              renderContent={renderContent}
            />
          ))
        ) : (
          <div className="el-tree__empty-block">
            <span className="el-tree__empty-text">暂无数据</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Tree;

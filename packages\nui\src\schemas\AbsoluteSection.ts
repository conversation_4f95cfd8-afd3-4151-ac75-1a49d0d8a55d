import { AbstractSchema, type BaseSchema } from './index'

export interface AbsoluteSectionProps {
  width?: number | string
  height?: number | string
  backgroundColor?: string
  borderColor?: string
  borderStyle?: 'solid' | 'dashed' | 'dotted' | 'none'
  borderWidth?: string | number
  borderRadius?: string | number
  padding?: string
  overflow?: 'visible' | 'hidden' | 'scroll' | 'auto'
}

export interface AbsoluteSectionSchema extends BaseSchema {
  compName: 'AbsoluteSection'
  props?: AbsoluteSectionProps
}

export class AbsoluteSection extends AbstractSchema {
  component: string = 'AbsoluteSection'
  name: string = '绝对定位容器'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  template?: string | undefined;
  
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return '<div class="absolute-section" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" style="position: relative; width: {{ $data["props"]->width ?? "100%" }}; height: {{ $data["props"]->height ?? "400px" }}; background-color: {{ $data["props"]->backgroundColor ?? "#f9f9f9" }}; border: {{ $data["props"]->borderWidth ?? "2px" }} {{ $data["props"]->borderStyle ?? "dashed" }} {{ $data["props"]->borderColor ?? "#cccccc" }}; border-radius: {{ $data["props"]->borderRadius ?? "8px" }}; padding: {{ $data["props"]->padding ?? "0" }}; overflow: {{ $data["props"]->overflow ?? "visible" }};"></div>'
  }
}

export default new AbsoluteSection([
  {
    ref: 'width',
    label: '宽度',
    format: 'string',
    default: '100%',
    bindable: true,
  },
  {
    ref: 'height',
    label: '高度',
    format: 'string',
    default: '400px',
    bindable: true,
  },
  {
    ref: 'backgroundColor',
    label: '背景颜色',
    format: 'string',
    default: '#f9f9f9',
    bindable: true,
  },
  {
    ref: 'borderColor',
    label: '边框颜色',
    format: 'string',
    default: '#cccccc',
    bindable: true,
  },
  {
    ref: 'borderStyle',
    label: '边框样式',
    format: 'string',
    default: 'dashed',
    bindable: true,
  },
  {
    ref: 'borderWidth',
    label: '边框宽度',
    format: 'string',
    default: '2px',
    bindable: true,
  },
  {
    ref: 'borderRadius',
    label: '圆角',
    format: 'string',
    default: '8px',
    bindable: true,
  },
  {
    ref: 'padding',
    label: '内边距',
    format: 'string',
    default: '0',
    bindable: true,
  }
])

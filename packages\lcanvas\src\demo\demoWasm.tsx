import { useState, useEffect } from 'react'

function DemoWasm() {
  const [phpWeb, setPhpWeb] = useState<any>(null)
  const [ready, setReady] = useState(false)
  const [output, setOutput] = useState('')
  const [code, setCode] = useState(`<?php
echo "Hello from PHP WASM!\\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\\n";

$data = ['name' => 'LCanvas', 'version' => '1.0.0'];
echo "Project: " . $data['name'] . " v" . $data['version'] . "\\n";

for($i = 1; $i <= 5; $i++) {
    echo "Count: $i\\n";
}
?>`)

  const loadPhpWasm = async () => {
    const host = `${window.location.protocol}//${window.location.host}`;
    console.log('host', host);
    const phpModule = await import(`${host}/lib/blade/PhpWeb.mjs`);
    const php = new phpModule.PhpWeb();
    setPhpWeb(php);
    const phpBinary = await php.binary;
    const zipResponse = await fetch(`${host}/lib/blade/blade-template.zip`);
    if (!zipResponse.ok) {
      throw new Error('无法下载 ZIP 文件: ' + zipResponse.status);
    }
    const zipData = await zipResponse.arrayBuffer();
    phpBinary.FS.writeFile('./blade-template.zip', new Uint8Array(zipData));
    if (!phpBinary.FS.analyzePath('./workspace').exists) {
      phpBinary.FS.mkdir('./workspace');
    }
    await php.run(`<?php
                     
                    $zip = new ZipArchive;
                    echo "zip....";

                    $reflection = new ReflectionClass('ZipArchive');
                    echo "类名：" . $reflection->getName() . "\\n";

                    $result = $zip->open("./blade-template.zip");
                    if ($result === TRUE) {
                        if (!$zip->extractTo("./workspace/")) {
                            exit(1);
                        }
                        $zip->close();
                    } else {
                        exit(1);
                    }
                    echo "after extract....";
                ?>`);
    await php.run(`<?php include_once './workspace/blade-template/index.php'; ?>`);
    console.log("ready.");
    setReady(true);
  };

  useEffect(() => {
    loadPhpWasm();
  }, []);

  const runCode = async () => {
    if (!phpWeb || !ready) {
      alert('PHP WASM 还未准备就绪');
      return;
    }

    setOutput('');
    
    phpWeb.addEventListener('output', (event: any) => {
      setOutput(prev => prev + event.detail);
    });

    try {
      await phpWeb.run(code);
    } catch (error) {
      setOutput(prev => prev + '\nError: ' + error);
    }
  };

  const runBladeTemplate = async () => {
    if (!phpWeb || !ready) {
      alert('PHP WASM 还未准备就绪');
      return;
    }

    setOutput('');
    
    phpWeb.addEventListener('output', (event: any) => {
      setOutput(prev => prev + event.detail);
    });

    const bladeCode = `<?php
// 设置一些变量
$name = 'LCanvas';
$version = '1.0.0';
$features = ['可视化设计', '组件化', '实时预览', 'WASM支持'];
$isAdmin = true;

// 使用 Blade 模板
echo view('demo', [
    'name' => $name,
    'version' => $version,
    'features' => $features,
    'isAdmin' => $isAdmin
]);
?>`;

    try {
      await phpWeb.run(bladeCode);
    } catch (error) {
      setOutput(prev => prev + '\nError: ' + error);
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <h1 style={{ marginBottom: '20px', color: '#333' }}>PHP WASM 演示</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <div style={{
          padding: '12px 16px',
          background: ready ? '#d4edda' : '#fff3cd',
          border: `1px solid ${ready ? '#c3e6cb' : '#ffeaa7'}`,
          borderRadius: '4px',
          color: ready ? '#155724' : '#856404'
        }}>
          状态: {ready ? '✅ PHP WASM 已就绪' : '⏳ 正在加载 PHP WASM...'}
        </div>
      </div>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px',
        marginBottom: '20px'
      }}>
        {/* 代码编辑区 */}
        <div>
          <h3 style={{ marginBottom: '10px', color: '#333' }}>PHP 代码</h3>
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            style={{
              width: '100%',
              height: '300px',
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              fontSize: '14px',
              lineHeight: '1.4',
              resize: 'vertical'
            }}
            placeholder="在这里输入 PHP 代码..."
          />
          
          <div style={{ marginTop: '10px', display: 'flex', gap: '10px' }}>
            <button
              onClick={runCode}
              disabled={!ready}
              style={{
                padding: '8px 16px',
                background: ready ? '#007bff' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: ready ? 'pointer' : 'not-allowed',
                fontSize: '14px'
              }}
            >
              运行 PHP 代码
            </button>
            
            <button
              onClick={runBladeTemplate}
              disabled={!ready}
              style={{
                padding: '8px 16px',
                background: ready ? '#28a745' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: ready ? 'pointer' : 'not-allowed',
                fontSize: '14px'
              }}
            >
              运行 Blade 模板
            </button>
          </div>
        </div>

        {/* 输出区 */}
        <div>
          <h3 style={{ marginBottom: '10px', color: '#333' }}>输出结果</h3>
          <pre style={{
            width: '100%',
            height: '300px',
            padding: '12px',
            background: '#f8f9fa',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            fontSize: '14px',
            lineHeight: '1.4',
            overflow: 'auto',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}>
            {output || '点击运行按钮查看输出...'}
          </pre>
        </div>
      </div>

      {/* 预设示例 */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #e5e5e5'
      }}>
        <h3 style={{ marginBottom: '15px', color: '#333' }}>预设示例</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
          <button
            onClick={() => setCode(`<?php
echo "Hello World!\\n";
echo "PHP Version: " . phpversion() . "\\n";
?>`)}
            style={{
              padding: '10px',
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Hello World
          </button>
          
          <button
            onClick={() => setCode(`<?php
$numbers = [1, 2, 3, 4, 5];
foreach($numbers as $num) {
    echo "Number: $num\\n";
}
?>`)}
            style={{
              padding: '10px',
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            循环示例
          </button>
          
          <button
            onClick={() => setCode(`<?php
class User {
    public $name;
    public function __construct($name) {
        $this->name = $name;
    }
    public function greet() {
        return "Hello, " . $this->name . "!";
    }
}

$user = new User("LCanvas");
echo $user->greet() . "\\n";
?>`)}
            style={{
              padding: '10px',
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            类和对象
          </button>
          
          <button
            onClick={() => setCode(`<?php
$data = [
    'project' => 'LCanvas',
    'language' => 'PHP',
    'features' => ['WASM', 'Blade', 'React']
];

echo json_encode($data, JSON_PRETTY_PRINT) . "\\n";
?>`)}
            style={{
              padding: '10px',
              background: 'white',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            JSON 处理
          </button>
        </div>
      </div>

      <div style={{
        marginTop: '20px',
        padding: '15px',
        background: '#e7f3ff',
        border: '1px solid #b3d9ff',
        borderRadius: '4px',
        fontSize: '14px',
        color: '#0066cc'
      }}>
        💡 <strong>提示:</strong> 这个演示展示了在浏览器中运行 PHP WASM 的能力。
        您可以编写 PHP 代码并实时查看执行结果，包括 Blade 模板的渲染。
      </div>
    </div>
  )
}

export default DemoWasm

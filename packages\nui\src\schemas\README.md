# Laravel Blade 模板引擎语法规则与示例

## 目录
- [基础语法](#基础语法)
- [数据输出](#数据输出)
- [控制结构](#控制结构)
- [循环结构](#循环结构)
- [包含和扩展](#包含和扩展)
- [组件和插槽](#组件和插槽)
- [表单处理](#表单处理)
- [CSRF 保护](#csrf-保护)
- [注释](#注释)
- [最佳实践](#最佳实践)

## 基础语法

### 1. 基本输出语法

```blade
{{-- 输出转义的内容 --}}
{{ $variable }}

{{-- 输出未转义的内容 --}}
{!! $html !!}

{{-- 输出默认值 --}}
{{ $variable ?? '默认值' }}

{{-- 输出方法调用结果 --}}
{{ $user->getName() }}
```

### 2. 变量赋值

```blade
@php
    $name = '<PERSON>';
    $age = 25;
@endphp

{{ $name }} 今年 {{ $age }} 岁
```

## 数据输出

### 1. 安全输出

```blade
{{-- 自动转义 HTML 标签 --}}
{{ $user->name }}

{{-- 不转义 HTML 标签 --}}
{!! $user->bio !!}
```

### 2. 条件输出

```blade
{{-- 使用三元运算符 --}}
{{ $user->isAdmin ? '管理员' : '普通用户' }}

{{-- 使用 null 合并运算符 --}}
{{ $user->nickname ?? $user->name }}
```

## 控制结构

### 1. if 语句

```blade
@if($user->isAdmin)
    <div class="admin-panel">
        管理员面板
    </div>
@elseif($user->isModerator)
    <div class="moderator-panel">
        版主面板
    </div>
@else
    <div class="user-panel">
        用户面板
    </div>
@endif
```

### 2. unless 语句（除非）

```blade
@unless($user->isAdmin)
    <p>您没有管理员权限</p>
@endunless
```

### 3. isset 检查

```blade
@isset($user->profile)
    <div class="profile">
        {{ $user->profile->bio }}
    </div>
@endisset

@empty($posts)
    <p>暂无文章</p>
@endempty
```

## 循环结构

### 1. foreach 循环

```blade
@foreach($users as $user)
    <div class="user-item">
        <h3>{{ $user->name }}</h3>
        <p>{{ $user->email }}</p>
    </div>
@endforeach
```

### 2. for 循环

```blade
@for($i = 0; $i < 10; $i++)
    <span class="number">{{ $i }}</span>
@endfor
```

### 3. while 循环

```blade
@while($condition)
    <div>循环内容</div>
@endwhile
```

### 4. 循环控制

```blade
@foreach($users as $user)
    @if($user->isAdmin)
        @continue
    @endif
    
    @if($loop->iteration > 5)
        @break
    @endif
    
    <div class="user">
        {{ $user->name }}
        @if($loop->first)
            (第一个用户)
        @endif
        @if($loop->last)
            (最后一个用户)
        @endif
    </div>
@endforeach
```

### 5. 循环变量

```blade
@foreach($items as $item)
    <div class="item">
        <p>索引: {{ $loop->index }}</p>
        <p>迭代: {{ $loop->iteration }}</p>
        <p>剩余: {{ $loop->remaining }}</p>
        <p>总数: {{ $loop->count }}</p>
        <p>深度: {{ $loop->depth }}</p>
        <p>父级: {{ $loop->parent }}</p>
    </div>
@endforeach
```

## 包含和扩展

### 1. 包含文件

```blade
{{-- 包含视图文件 --}}
@include('partials.header')

{{-- 包含并传递数据 --}}
@include('partials.user-card', ['user' => $user])

{{-- 包含并传递所有变量 --}}
@include('partials.user-card', compact('user', 'posts'))
```

### 2. 条件包含

```blade
@includeWhen($user->isAdmin, 'partials.admin-panel')

@includeUnless($user->isGuest, 'partials.user-menu')
```

### 3. 模板继承

```blade
{{-- 父模板 (layouts/app.blade.php) --}}
<!DOCTYPE html>
<html>
<head>
    <title>@yield('title', '默认标题')</title>
    @stack('styles')
</head>
<body>
    @include('partials.header')
    
    <main>
        @yield('content')
    </main>
    
    @include('partials.footer')
    @stack('scripts')
</body>
</html>

{{-- 子模板 (pages/home.blade.php) --}}
@extends('layouts.app')

@section('title', '首页')

@push('styles')
    <link rel="stylesheet" href="/css/home.css">
@endpush

@section('content')
    <h1>欢迎来到首页</h1>
    <p>这是主要内容区域</p>
@endsection

@push('scripts')
    <script src="/js/home.js"></script>
@endpush
```

### 4. 组件包含

```blade
@includeFirst(['partials.header', 'partials.default-header'])
```

## 组件和插槽

### 1. 基础组件

```blade
{{-- 定义组件 (resources/views/components/alert.blade.php) --}}
<div class="alert alert-{{ $type }}">
    {{ $slot }}
</div>

{{-- 使用组件 --}}
<x-alert type="success">
    操作成功！
</x-alert>
```

### 2. 具名插槽

```blade
{{-- 定义组件 (resources/views/components/card.blade.php) --}}
<div class="card">
    <div class="card-header">
        {{ $header }}
    </div>
    <div class="card-body">
        {{ $slot }}
    </div>
    <div class="card-footer">
        {{ $footer }}
    </div>
</div>

{{-- 使用组件 --}}
<x-card>
    <x-slot name="header">
        <h3>卡片标题</h3>
    </x-slot>
    
    卡片内容
    
    <x-slot name="footer">
        <button>操作按钮</button>
    </x-slot>
</x-card>
```

### 3. 内联组件

```blade
@component('components.alert', ['type' => 'error'])
    <strong>错误！</strong> 发生了问题。
@endcomponent
```

## 表单处理

### 1. CSRF 令牌

```blade
<form method="POST" action="/users">
    @csrf
    <input type="text" name="name">
    <button type="submit">提交</button>
</form>
```

### 2. 表单验证错误

```blade
<form method="POST" action="/users">
    @csrf
    
    <div class="form-group">
        <label>用户名</label>
        <input type="text" name="username" value="{{ old('username') }}">
        @error('username')
            <span class="error">{{ $message }}</span>
        @enderror
    </div>
    
    <div class="form-group">
        <label>邮箱</label>
        <input type="email" name="email" value="{{ old('email') }}">
        @error('email')
            <span class="error">{{ $message }}</span>
        @enderror
    </div>
</form>
```

### 3. 表单方法欺骗

```blade
<form method="POST" action="/users/1">
    @csrf
    @method('PUT')
    <input type="text" name="name" value="{{ $user->name }}">
    <button type="submit">更新</button>
</form>

<form method="POST" action="/users/1">
    @csrf
    @method('DELETE')
    <button type="submit">删除</button>
</form>
```

## CSRF 保护

### 1. 基本使用

```blade
{{-- 在表单中添加 CSRF 令牌 --}}
<form method="POST">
    @csrf
    {{-- 表单内容 --}}
</form>

{{-- 在 AJAX 请求中添加 --}}
<meta name="csrf-token" content="{{ csrf_token() }}">

<script>
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
</script>
```

### 2. 排除 CSRF 保护

```php
// 在 app/Http/Middleware/VerifyCsrfToken.php 中
protected $except = [
    'api/*',
    'webhook/*'
];
```

## 注释

### 1. Blade 注释

```blade
{{-- 这是 Blade 注释，不会输出到 HTML --}}

{{-- 
    多行注释
    可以跨越多行
--}}
```

#
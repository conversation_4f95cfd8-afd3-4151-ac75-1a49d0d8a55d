/**
 * 缓存数据接口
 */
interface CacheData {
  version: string;
  timestamp: number;
  isWASMReady: boolean;
  phpInstanceData?: any; // 可以存储 PHP 实例的序列化数据
}

/**
 * WASM 缓存管理器
 * 负责管理 BladeRender WASM 状态的缓存和持久化
 */
export class WASMCacheManager {
  private static readonly CACHE_KEY = 'blade_render_cache';
  private static readonly CACHE_VERSION = '1.0.0';
  private static readonly CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24小时过期

  /**
   * 检查缓存是否有效
   */
  static isValidCache(): boolean {
    try {
      const cached = localStorage.getItem(WASMCacheManager.CACHE_KEY);
      if (!cached) return false;

      const cacheData: CacheData = JSON.parse(cached);
      const now = Date.now();
      
      return (
        cacheData.version === WASMCacheManager.CACHE_VERSION &&
        cacheData.timestamp &&
        (now - cacheData.timestamp) < WASMCacheManager.CACHE_EXPIRY &&
        cacheData.isWASMReady === true
      );
    } catch (error) {
      console.warn('检查 WASM 缓存失败:', error);
      return false;
    }
  }

  /**
   * 从缓存中获取 WASM 状态
   */
  static getCachedState(): boolean {
    try {
      if (WASMCacheManager.isValidCache()) {
        const cached = localStorage.getItem(WASMCacheManager.CACHE_KEY);
        if (cached) {
          const cacheData: CacheData = JSON.parse(cached);
          console.log('从缓存中恢复 WASM 状态');
          return cacheData.isWASMReady;
        }
      }
    } catch (error) {
      console.warn('读取 WASM 缓存失败:', error);
    }
    return false;
  }

  /**
   * 保存 WASM 状态到缓存
   */
  static saveToCache(isWASMReady: boolean, phpInstanceData?: any): void {
    try {
      const cacheData: CacheData = {
        version: WASMCacheManager.CACHE_VERSION,
        timestamp: Date.now(),
        isWASMReady,
        phpInstanceData
      };
      
      localStorage.setItem(WASMCacheManager.CACHE_KEY, JSON.stringify(cacheData));
      console.log('WASM 状态已保存到缓存');
    } catch (error) {
      console.warn('保存 WASM 缓存失败:', error);
    }
  }

  /**
   * 清理缓存
   */
  static clearCache(): void {
    try {
      localStorage.removeItem(WASMCacheManager.CACHE_KEY);
      console.log('WASM 缓存已清理');
    } catch (error) {
      console.warn('清理 WASM 缓存失败:', error);
    }
  }

  /**
   * 获取缓存信息（用于调试）
   */
  static getCacheInfo(): any {
    try {
      const cached = localStorage.getItem(WASMCacheManager.CACHE_KEY);
      if (cached) {
        const cacheData = JSON.parse(cached);
        const now = Date.now();
        const age = now - cacheData.timestamp;
        const isExpired = age > WASMCacheManager.CACHE_EXPIRY;
        
        return {
          ...cacheData,
          age: Math.floor(age / 1000), // 秒
          isExpired,
          isValid: WASMCacheManager.isValidCache()
        };
      }
    } catch (error) {
      console.warn('获取缓存信息失败:', error);
    }
    return null;
  }

  /**
   * 强制刷新缓存（清理旧缓存并重新加载）
   */
  static forceRefresh(): void {
    WASMCacheManager.clearCache();
    console.log('缓存已强制刷新，下次加载将重新初始化 WASM');
  }
}



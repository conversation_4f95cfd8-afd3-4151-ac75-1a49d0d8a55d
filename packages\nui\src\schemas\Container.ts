import { AbstractSchema, type BaseSchema } from './index'

export interface ContainerProps {
  width?: string | number
  height?: string | number
  backgroundColor?: string
  padding?: string
  margin?: string
  borderRadius?: string | number
  border?: string
  boxShadow?: string
  display?: 'block' | 'flex' | 'inline-block' | 'inline-flex' | 'grid'
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse'
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly'
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch'
  overflow?: 'visible' | 'hidden' | 'scroll' | 'auto'
}

export interface ContainerSchema extends BaseSchema {
  compName: 'div'
  props?: ContainerProps
}


export class Container extends AbstractSchema {
  component: string = 'Container'
  name: string = '容器'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return '<div class="container-element" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" style="width: {{ $data["props"]->width ?? "auto" }}; height: {{ $data["props"]->height ?? "auto" }}; background-color: {{ $data["props"]->backgroundColor ?? "transparent" }}; padding: {{ $data["props"]->padding ?? "0" }}; margin: {{ $data["props"]->margin ?? "0" }}; border-radius: {{ $data["props"]->borderRadius ?? "0" }}; border: {{ $data["props"]->border ?? "none" }}; box-shadow: {{ $data["props"]->boxShadow ?? "none" }}; display: {{ $data["props"]->display ?? "block" }}; flex-direction: {{ $data["props"]->flexDirection ?? "row" }}; justify-content: {{ $data["props"]->justifyContent ?? "flex-start" }}; align-items: {{ $data["props"]->alignItems ?? "stretch" }}; overflow: {{ $data["props"]->overflow ?? "visible" }};"></div>'
  }

}

export default new Container([
  {
    ref: 'width',
    label: '宽度',
    format: 'string',
    default: 'auto',
    bindable: true,
  },
  {
    ref: 'height',
    label: '高度',
    format: 'string',
    default: 'auto',
    bindable: true,
  },
  {
    ref: 'backgroundColor',
    label: '背景颜色',
    format: 'string',
    default: 'transparent',
    bindable: true,
  },
  {
    ref: 'padding',
    label: '内边距',
    format: 'string',
    default: '0',
    bindable: true,
  },
  {
    ref: 'margin',
    label: '外边距',
    format: 'string',
    default: '0',
    bindable: true,
  },
  {
    ref: 'borderRadius',
    label: '圆角',
    format: 'string',
    default: '0',
    bindable: true,
  },
  {
    ref: 'display',
    label: '显示方式',
    format: 'string',
    default: 'block',
    bindable: true,
  },
  {
    ref: 'flexDirection',
    label: 'Flex方向',
    format: 'string',
    default: 'row',
    bindable: true,
  },
  {
    ref: 'justifyContent',
    label: '主轴对齐',
    format: 'string',
    default: 'flex-start',
    bindable: true,
  },
  {
    ref: 'alignItems',
    label: '交叉轴对齐',
    format: 'string',
    default: 'stretch',
    bindable: true,
  }
])

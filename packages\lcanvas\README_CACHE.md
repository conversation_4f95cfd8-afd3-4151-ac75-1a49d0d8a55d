# LCanvas WASM 缓存优化 - 快速开始

## 🎯 解决的问题

在 LCanvas 模式下，每次访问 `/comp` 页面都需要重新加载 WASM，导致：
- 加载时间长（3-5秒）
- 用户体验差
- 资源浪费

## ✨ 优化效果

- **首次加载**: 3-5秒（正常）
- **后续访问**: <100ms（从缓存恢复）
- **缓存有效期**: 24小时
- **自动失效**: 版本更新时自动清理

## 🚀 快速使用

### 1. 基础用法（推荐）

```tsx
import { useBladeRender, BladeView } from 'lcanvas';

function MyComponent() {
  const { isReady, isLoading, error, render, ensureReady } = useBladeRender();

  useEffect(() => {
    const init = async () => {
      await ensureReady(); // 自动处理缓存
      if (render) {
        await render.defineVariable("myVar", "value");
      }
    };
    init();
  }, [ensureReady, render]);

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return <BladeView schema={mySchema} />;
}
```

### 2. 简化用法

```tsx
import { useBladeRenderReady, BladeView } from 'lcanvas';

function SimpleComponent() {
  const { isReady } = useBladeRenderReady(); // 自动初始化

  if (!isReady) return <div>准备中...</div>;
  
  return <BladeView schema={mySchema} />;
}
```

### 3. 添加调试面板

```tsx
import { CacheDebugPanel } from 'lcanvas';

function App() {
  return (
    <>
      <CacheDebugPanel /> {/* 右上角显示缓存状态 */}
      <MyComponent />
    </>
  );
}
```

## 🧪 测试缓存功能

访问测试页面：`/cache-test`

该页面提供：
- 实时缓存状态显示
- 加载时间统计
- 缓存操作按钮
- 详细的测试记录

## 📊 缓存管理

### 手动操作

```tsx
import { BladeRender, WASMCacheManager } from 'lcanvas';

// 清理缓存
BladeRender.clearCache();

// 强制刷新
BladeRender.forceRefresh();

// 获取缓存信息
const info = BladeRender.getCacheInfo();
console.log('缓存状态:', info);
```

### 缓存信息

```tsx
{
  version: "1.0.0",        // 缓存版本
  timestamp: 1703123456789, // 创建时间
  age: 3600,               // 存在时长（秒）
  isValid: true,           // 是否有效
  isExpired: false         // 是否过期
}
```

## 🔧 高级配置

### 自定义缓存策略

```tsx
// 开发环境强制刷新
if (process.env.NODE_ENV === 'development') {
  BladeRender.forceRefresh();
}

// 检查缓存有效性
if (WASMCacheManager.isValidCache()) {
  console.log('缓存有效，快速启动');
} else {
  console.log('缓存无效，需要重新加载');
}
```

### 错误处理

```tsx
const { error, ensureReady } = useBladeRender();

useEffect(() => {
  ensureReady().catch(err => {
    console.error('WASM 初始化失败:', err);
    // 可以显示用户友好的错误信息
    // 或者尝试重新加载
  });
}, [ensureReady]);
```

## 📁 文件结构

```
packages/lcanvas/src/
├── core/
│   ├── cache/
│   │   └── WASMCacheManager.ts    # 缓存管理器
│   └── render/
│       └── BladeRender.ts         # 增强的渲染器
├── hooks/
│   └── useBladeRender.ts          # React Hook
├── components/
│   └── CacheDebugPanel.tsx        # 调试面板
├── CacheTestPage.tsx              # 测试页面
└── index.ts                       # 导出文件
```

## 🐛 故障排除

### 常见问题

1. **缓存不生效**
   - 检查浏览器是否支持 localStorage
   - 确认缓存版本是否匹配

2. **加载失败**
   - 检查网络连接
   - 确认 WASM 文件路径正确
   - 使用调试面板查看详细错误

3. **状态不同步**
   - 使用 `forceRefresh()` 重置状态
   - 清理浏览器缓存

### 调试技巧

```tsx
// 查看详细缓存信息
console.log('缓存详情:', BladeRender.getCacheInfo());

// 监控加载性能
const startTime = Date.now();
await ensureReady();
console.log('加载耗时:', Date.now() - startTime, 'ms');
```

## 🔄 迁移指南

### 从旧版本升级

**旧代码：**
```tsx
useEffect(() => {
  (async function () {
    let render = BladeRender.getInstance();
    if (!BladeRender.isWASMReady) {
      await render.loadWASM();
    }
    await render.defineVariable("isAdmin", "0");
    setSchema(examleSchema);
  })()
}, [])
```

**新代码：**
```tsx
const { ensureReady, render } = useBladeRender();

useEffect(() => {
  const init = async () => {
    await ensureReady();
    if (render) {
      await render.defineVariable("isAdmin", "0");
    }
    setSchema(examleSchema);
  };
  init();
}, [ensureReady, render]);
```

## 📈 性能监控

使用 `CacheDebugPanel` 可以实时监控：
- WASM 加载状态
- 缓存命中率
- 加载时间统计
- 错误信息

## 🎉 总结

通过实施缓存优化方案：
- ✅ 显著提升加载速度
- ✅ 改善用户体验
- ✅ 减少资源消耗
- ✅ 提供完善的调试工具
- ✅ 保持向后兼容

现在您可以享受更快的 LCanvas 加载体验！

import React, { useState, useRef, useCallback } from 'react'
import type { BaseSchema } from 'nui'

export type AbsoluteSectionProps = {
  schema?: BaseSchema
  width?: number | string
  height?: number | string
  onSchemaChange?: (schema: BaseSchema) => void
}

export const AbsoluteSection = (props: AbsoluteSectionProps) => {
  const {
    schema,
    width = '100%',
    height = '400px',
    onSchemaChange
  } = props
  // 初始化子组件数据，如果没有传入schema则使用默认数据
  const initializeChildren = () => {
    if (schema?.body) {
      return Object.keys(schema.body).map(key => {
        const childSchema = schema.body![key]
        return {
          id: childSchema.id,
          x: 50 + Math.random() * 200, // 随机初始位置
          y: 50 + Math.random() * 150,
          schema: childSchema
        }
      })
    }
    // 默认示例数据
    return [
      {
        id: 'btn1',
        x: 50,
        y: 50,
        schema: {
          component: 'button',
          id: 'btn1',
          name: '按钮1'
        } as BaseSchema
      },
      {
        id: 'btn2',
        x: 200,
        y: 100,
        schema: {
          component: 'button',
          id: 'btn2',
          name: '按钮2'
        } as BaseSchema
      }
    ]
  }

  const [children, setChildren] = useState(initializeChildren)

  // 监听 schema 变化，同步更新 children
  React.useEffect(() => {
    if (schema?.body) {
      setChildren(prevChildren => {
        const schemaKeys = Object.keys(schema.body!)
        const currentKeys = prevChildren.map(child => child.id)

        // 检查是否有变化
        if (schemaKeys.length !== currentKeys.length ||
            !schemaKeys.every(key => currentKeys.includes(schema.body![key].id))) {

          const newChildren = schemaKeys.map(key => {
            const childSchema = schema.body![key]
            // 检查是否已存在该组件，如果存在则保持原有位置
            const existingChild = prevChildren.find(child => child.id === childSchema.id)
            return {
              id: childSchema.id,
              x: existingChild?.x ?? (50 + Math.random() * 200),
              y: existingChild?.y ?? (50 + Math.random() * 150),
              schema: childSchema
            }
          })
          return newChildren
        }
        return prevChildren
      })
    } else {
      // 如果没有 body，清空 children
      setChildren([])
    }
  }, [schema])

  const [dragState, setDragState] = useState<{
    isDragging: boolean
    dragId: string | null
    startX: number
    startY: number
    offsetX: number
    offsetY: number
  }>({
    isDragging: false,
    dragId: null,
    startX: 0,
    startY: 0,
    offsetX: 0,
    offsetY: 0
  })

  const containerRef = useRef<HTMLDivElement>(null)

  // 渲染子组件
  const renderChild = (childSchema: BaseSchema) => {
    switch (childSchema.component) {
      case 'button':
        return (
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {childSchema.name || '按钮'}
          </button>
        )
      case 'div':
        return (
          <div
            style={{
              padding: '10px',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              minWidth: '80px',
              minHeight: '40px'
            }}
          >
            {childSchema.name || 'Div容器'}
          </div>
        )
      case 'AbsoluteSection':
        // 支持嵌套 AbsoluteSection
        return (
          <div
            onDoubleClick={(e) => e.stopPropagation()}
            style={{ display: 'inline-block' }}
          >
            <AbsoluteSection
              schema={childSchema}
              width={200}
              height={150}
              onSchemaChange={(newSchema) => {
                // 更新嵌套组件的 schema
                if (onSchemaChange && schema) {
                  const updatedSchema = { ...schema }
                  if (updatedSchema.body) {
                    updatedSchema.body[childSchema.id] = newSchema
                    onSchemaChange(updatedSchema)
                  }
                }
              }}
            />
          </div>
        )
      default:
        return (
          <div
            style={{
              padding: '8px',
              backgroundColor: '#ffc107',
              border: '1px solid #fd7e14',
              borderRadius: '4px'
            }}
          >
            {childSchema.component}
          </div>
        )
    }
  }

  const handleMouseDown = useCallback((e: React.MouseEvent, itemId: string) => {
    e.preventDefault()
    e.stopPropagation()

    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const item = children.find(item => item.id === itemId)
    if (!item) return

    setDragState({
      isDragging: true,
      dragId: itemId,
      startX: e.clientX,
      startY: e.clientY,
      offsetX: e.clientX - rect.left - item.x,
      offsetY: e.clientY - rect.top - item.y
    })
  }, [children])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!dragState.isDragging || !dragState.dragId) return

    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const newX = e.clientX - rect.left - dragState.offsetX
    const newY = e.clientY - rect.top - dragState.offsetY

    const boundedX = Math.max(0, Math.min(newX, rect.width - 100))
    const boundedY = Math.max(0, Math.min(newY, rect.height - 40))

    setChildren(prevChildren =>
      prevChildren.map(child =>
        child.id === dragState.dragId ? { ...child, x: boundedX, y: boundedY } : child
      )
    )
  }, [dragState])

  const handleMouseUp = useCallback(() => {
    setDragState({
      isDragging: false,
      dragId: null,
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0
    })
  }, [])

  React.useEffect(() => {
    if (dragState.isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [dragState.isDragging, handleMouseMove, handleMouseUp])

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    // 阻止事件冒泡，防止在嵌套容器中触发父级容器的双击事件
    e.stopPropagation()

    const rect = containerRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // 根据按键决定添加的组件类型
    let componentType = 'button'
    let componentName = '新按钮'

    if (e.shiftKey) {
      componentType = 'div'
      componentName = '新容器'
    } else if (e.ctrlKey || e.metaKey) {
      componentType = 'AbsoluteSection'
      componentName = '嵌套容器'
    }

    const timestamp = Date.now()
    const newChildId = `${componentType}-${timestamp}`
    const newChild = {
      id: newChildId,
      x,
      y,
      schema: {
        component: componentType,
        id: newChildId,
        name: componentName,
        body: componentType === 'AbsoluteSection' ? {} : undefined
      } as BaseSchema
    }

    setChildren(prev => [...prev, newChild])

    // 如果有回调函数，更新父级schema
    if (onSchemaChange && schema) {
      const newSchema = { ...schema }
      if (!newSchema.body) newSchema.body = {}
      newSchema.body[newChild.id] = newChild.schema
      onSchemaChange(newSchema)
    }
  }, [onSchemaChange, schema])

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width,
        height,
        border: '2px dashed #ccc',
        borderRadius: '8px',
        backgroundColor: '#f9f9f9',
        overflow: 'hidden',
        cursor: 'crosshair'
      }}
      onDoubleClick={handleDoubleClick}
    >
      {children.map((child) => (
        <div
          key={child.id}
          style={{
            position: 'absolute',
            left: child.x,
            top: child.y,
            cursor: dragState.dragId === child.id ? 'grabbing' : 'grab',
            userSelect: 'none',
            zIndex: dragState.dragId === child.id ? 1000 : 1
          }}
          onMouseDown={(e) => handleMouseDown(e, child.id)}
        >
          {renderChild(child.schema)}
        </div>
      ))}

      {children.length === 0 && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#999',
            fontSize: '12px',
            pointerEvents: 'none',
            textAlign: 'center',
            lineHeight: '1.5'
          }}
        >
          双击添加按钮<br/>
          Shift+双击添加容器<br/>
          Ctrl+双击添加嵌套容器<br/>
          拖拽移动位置
        </div>
      )}
    </div>
  )
}

export default AbsoluteSection
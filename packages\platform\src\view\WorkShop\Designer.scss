.designer-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;

  .designer-main {
    flex: 1;
    margin-left: 60px; // 为左侧菜单留出空间
    display: flex;
    flex-direction: column;
    height: 100vh;

    .canvas-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      background: white;
      border-bottom: 1px solid #e5e5e5;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .canvas-mode-switcher {
        display: flex;
        gap: 8px;

        .mode-btn {
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          color: #666;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s ease;

          &:hover {
            border-color: #007bff;
            color: #007bff;
          }

          &.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
          }
        }
      }

      .canvas-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          padding: 8px 16px;
          background: #28a745;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s ease;

          &:hover {
            background: #218838;
            transform: translateY(-1px);
          }
        }
      }
    }

    .canvas-area {
      flex: 1;
      padding: 20px;
      background: white;
      margin: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: auto;
      position: relative;

      // 画布区域的拖拽提示
      &::before {
        content: "拖拽组件到此处或双击添加组件";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #999;
        font-size: 14px;
        pointer-events: none;
        z-index: 1;
        opacity: 0.5;
      }

      &:not(:empty)::before {
        display: none;
      }
    }

    .info-panel {
      width: 300px;
      background: white;
      border-left: 1px solid #e5e5e5;
      padding: 20px;
      overflow-y: auto;
      position: fixed;
      right: 0;
      top: 0;
      height: 100vh;
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);

      h4 {
        margin: 0 0 16px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e5e5;
      }

      .schema-preview {
        h5 {
          margin: 16px 0 8px 0;
          color: #666;
          font-size: 14px;
          font-weight: 500;
        }

        pre {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 4px;
          font-size: 12px;
          line-height: 1.4;
          overflow-x: auto;
          border: 1px solid #e5e5e5;
          color: #333;
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }
}

// 当信息面板显示时，调整主区域宽度
.designer-container:has(.info-panel) .designer-main {
  margin-right: 300px;
}

// 拖拽相关样式
.canvas-area {
  &.drag-over {
    border: 2px dashed #007bff;
    background: rgba(0, 123, 255, 0.05);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .designer-main .info-panel {
    width: 250px;
  }
  
  .designer-container:has(.info-panel) .designer-main {
    margin-right: 250px;
  }
}

@media (max-width: 768px) {
  .designer-container {
    flex-direction: column;
  }

  .designer-main {
    margin-left: 0;
    margin-right: 0;

    .canvas-area {
      margin: 10px;
      padding: 15px;
    }

    .info-panel {
      position: relative;
      width: 100%;
      height: auto;
      max-height: 200px;
      right: auto;
      top: auto;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .designer-container:has(.info-panel) .designer-main {
    margin-right: 0;
  }
}

// 加载状态
.canvas-area.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::before {
    content: "加载中...";
    color: #666;
  }
}

// 空状态
.canvas-area.empty {
  border: 2px dashed #ddd;
  
  &::before {
    content: "拖拽组件到此处开始设计";
    color: #999;
    font-size: 16px;
  }
}

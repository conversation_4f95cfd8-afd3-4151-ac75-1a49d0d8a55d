/**
 * Element UI Tree 组件样式
 * 基于 Element UI 设计规范的树形组件样式定义
 * 包含完整的主题变量和组件样式，优化后的简洁版本
 */

// ===== 主题变量定义 =====
// 主色调
$--color-primary: #409eff;
$--color-primary-light-1: #53a8ff;
$--color-primary-light-2: #66b1ff;
$--color-primary-light-3: #79bbff;
$--color-primary-light-4: #8cc5ff;
$--color-primary-light-5: #a0cfff;
$--color-primary-light-6: #b3d8ff;
$--color-primary-light-7: #c6e2ff;
$--color-primary-light-8: #d9ecff;
$--color-primary-light-9: #ecf5ff;

// 功能色彩
$--color-success: #67c23a;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #909399;

// 文字颜色
$--color-text-primary: #303133;
$--color-text-regular: #606266;
$--color-text-secondary: #909399;
$--color-text-placeholder: #c0c4cc;

// 边框颜色
$--border-color-base: #dcdfe6;
$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;
$--border-color-extra-light: #f2f6fc;

// 背景颜色
$--background-color-base: #f5f7fa;
$--background-color-light: #fafafa;

// 字体大小
$--font-size-base: 14px;
$--font-size-small: 13px;
$--font-size-mini: 12px;

// 圆角半径
$--border-radius-base: 4px;
$--border-radius-small: 2px;

// 阴影效果
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// ===== Mixins =====

// 文本省略混入
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Flex 居中混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// ===== 主容器样式 =====
/**
 * Tree 主容器
 * 定义树形组件的基础容器样式
 */
.el-tree {
  position: relative;
  cursor: default;
  background: #fff;
  color: $--color-text-regular;
  font-size: $--font-size-base;
  line-height: 1.5;

  // 搜索框容器
  &__search {
    padding: 8px;
    border-bottom: 1px solid $--border-color-lighter;
    margin-bottom: 4px;
    background: $--background-color-light;
  }

  // 空状态容器
  &__empty-block {
    position: relative;
    min-height: 60px;
    width: 100%;
    height: 100%;
    @include flex-center;
  }

  // 空状态文本
  &__empty-text {
    color: $--color-text-secondary;
    font-size: $--font-size-base;
    user-select: none;
  }

  // 内容区域
  &__content {
    padding: 4px 0;
  }

  // 拖拽指示器
  &__drop-indicator {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background-color: $--color-primary;
    border-radius: 1px;
    opacity: 0;
    pointer-events: none;

    &.is-active {
      opacity: 1;
    }
  }
}

// ===== 树节点样式 =====
/**
 * 树节点容器
 * 定义单个树节点的基础样式
 */
.el-tree-node {
  white-space: nowrap;
  outline: none;

  // 焦点状态
  &:focus > .el-tree-node__content {
    background-color: $--background-color-base;
    outline: 1px solid $--color-primary;
    outline-offset: -1px;
  }



  // 节点内容容器
  &__content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 26px;
    cursor: pointer;
    position: relative;
    padding: 0 20px 0 0;
    border-radius: $--border-radius-small;

    // 悬停状态
    &:hover {
      background-color: $--background-color-base;
    }

    // 禁用状态
    &.is-disabled {
      color: $--color-text-placeholder;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }

    // 当前选中状态
    &.is-current {
      background-color: $--color-primary-light-9;
      color: $--color-primary;
      font-weight: 500;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: $--color-primary;
        border-radius: 0 2px 2px 0;
      }
    }

    // 节点展开时的样式
    &.is-expanded {
      font-weight: 500;
    }

    // 活跃状态
    &:active {
      background-color: $--color-primary-light-8;
    }

    // 拖拽状态
    &.is-dragging {
      opacity: 0.5;
      background-color: $--color-primary-light-9;
    }

    // 拖拽悬停状态
    &.is-drop-prev::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: $--color-primary;
    }

    &.is-drop-next::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: $--color-primary;
    }

    &.is-drop-inner {
      background-color: $--color-primary-light-9;
      border: 1px dashed $--color-primary;
    }
  }

  // 展开/收起图标
  &__expand-icon {
    @include flex-center;
    cursor: pointer;
    color: $--color-text-secondary;
    font-size: 12px;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    user-select: none;

    // 展开状态
    &.el-icon-caret-bottom {
      color: $--color-primary;
      transform: rotate(90deg);
    }

    // 收起状态
    &.el-icon-caret-right {
      color: $--color-text-secondary;
    }

    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      cursor: default;
      pointer-events: none;
    }

    // 悬停效果
    &:hover:not(.is-leaf) {
      color: $--color-primary;
    }
  }

  // 节点标签文本
  &__label {
    font-size: $--font-size-base;
    color: $--color-text-regular;
    flex: 1;
    text-align: left;
    @include text-ellipsis;
    user-select: none;
    line-height: 1.4;

    // 高亮搜索关键词
    .highlight {
      background-color: $--color-warning;
      color: #fff;
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 500;
    }
  }

  // 加载图标
  &__loading-icon {
    margin-right: 8px;
    font-size: $--font-size-base;
    color: $--color-text-placeholder;
  }

  // 子节点容器
  &__children {
    overflow: hidden;
    background-color: transparent;
    transform-origin: top;


  }

  &.is-hidden {
    display: none;
  }

  &.is-focusable {
    .el-tree-node__content {
      &:focus {
        outline: 1px solid $--color-primary;
        outline-offset: -1px;
        background-color: $--background-color-base;
      }
    }
  }

  &.is-checked {
    .el-tree-node__content {
      font-weight: 700;
    }
  }
}

// 复选框样式
.el-checkbox {
  color: $--color-text-regular;
  font-weight: 500;
  font-size: $--font-size-base;
  position: relative;
  cursor: pointer;
  display: inline-block;
  white-space: nowrap;
  user-select: none;
  margin-right: 8px;

  &.is-disabled {
    cursor: not-allowed;
  }

  &__input {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-block;
    line-height: 1;
    position: relative;
    vertical-align: middle;

    &.is-disabled {
      cursor: not-allowed;

      .el-checkbox__inner {
        background-color: #edf2fc;
        border-color: $--border-color-lighter;
        cursor: not-allowed;

        &::after {
          cursor: not-allowed;
          border-color: $--color-text-placeholder;
        }
      }

      & + .el-checkbox__label {
        cursor: not-allowed;
      }

      &.is-checked {
        .el-checkbox__inner {
          background-color: $--border-color-lighter;
          border-color: $--border-color-lighter;

          &::after {
            border-color: #c0c4cc;
          }
        }
      }

      &.is-indeterminate {
        .el-checkbox__inner {
          background-color: $--border-color-lighter;
          border-color: $--border-color-lighter;

          &::before {
            background-color: #c0c4cc;
            border-color: #c0c4cc;
          }
        }
      }
    }

    &.is-checked {
      .el-checkbox__inner {
        background-color: $--color-primary;
        border-color: $--color-primary;

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }

      & + .el-checkbox__label {
        color: $--color-primary;
      }
    }

    &.is-focus {
      .el-checkbox__inner {
        border-color: $--color-primary;
      }
    }

    &.is-indeterminate {
      .el-checkbox__inner {
        background-color: $--color-primary;
        border-color: $--color-primary;

        &::before {
          content: '';
          position: absolute;
          display: block;
          background-color: #fff;
          height: 2px;
          transform: scale(0.5);
          left: 0;
          right: 0;
          top: 5px;
        }

        &::after {
          display: none;
        }
      }
    }
  }

  &__inner {
    display: inline-block;
    position: relative;
    border: 1px solid $--border-color-base;
    border-radius: $--border-radius-small;
    box-sizing: border-box;
    width: 14px;
    height: 14px;
    background-color: #fff;
    z-index: 1;
    transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46),
      background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);

    &:hover {
      border-color: $--color-primary;
    }

    &::after {
      box-sizing: content-box;
      content: '';
      border: 1px solid #fff;
      border-left: 0;
      border-top: 0;
      height: 7px;
      left: 4px;
      position: absolute;
      top: 1px;
      transform: rotate(45deg) scaleY(0);
      width: 3px;
      transition: transform 0.15s ease-in 0.05s;
      transform-origin: center;
    }
  }

  &__original {
    opacity: 0;
    outline: none;
    position: absolute;
    margin: 0;
    width: 0;
    height: 0;
    z-index: -1;
  }

  &__label {
    display: inline-block;
    padding-left: 10px;
    line-height: 19px;
    font-size: $--font-size-base;
  }
}



// ===== 搜索输入框样式 =====
/**
 * 搜索输入框组件
 * 提供树形组件的搜索功能界面
 */
.el-input {
  position: relative;
  font-size: $--font-size-base;
  display: inline-block;
  width: 100%;

  // 输入框内部元素
  &__inner {
    appearance: none;
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: $--border-radius-base;
    border: 1px solid $--border-color-base;
    box-sizing: border-box;
    color: $--color-text-regular;
    display: inline-block;
    font-size: inherit;
    height: 32px;
    line-height: 32px;
    outline: none;
    padding: 0 15px;
    width: 100%;

    // 焦点状态
    &:focus {
      outline: none;
      border-color: $--color-primary;
      box-shadow: 0 0 0 2px rgba($--color-primary, 0.2);
    }

    // 占位符样式
    &::placeholder {
      color: $--color-text-placeholder;
      font-size: $--font-size-small;
    }

    // 悬停状态
    &:hover:not(:disabled) {
      border-color: $--color-text-secondary;
    }

    // 禁用状态
    &:disabled {
      background-color: $--background-color-base;
      border-color: $--border-color-lighter;
      color: $--color-text-placeholder;
      cursor: not-allowed;
    }
  }

  &__suffix {
    position: absolute;
    height: 100%;
    right: 5px;
    top: 0;
    text-align: center;
    color: $--color-text-placeholder;
    transition: all 0.3s;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__icon {
    height: inherit;
    line-height: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    margin-left: 5px;

    &.el-icon-search {
      font-size: 14px;
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .el-tree-node__content {
    padding: 0 10px 0 0;
  }

  .el-tree-node__expand-icon {
    margin-right: 4px;
  }
}

import { AbstractSchema, type BaseSchema } from './index'

export interface TextProps {
  content?: string
  fontSize?: string | number
  color?: string
  fontWeight?: string | number
  textAlign?: 'left' | 'center' | 'right' | 'justify'
  lineHeight?: string | number
  fontFamily?: string
  textDecoration?: 'none' | 'underline' | 'line-through'
}

export interface TextSchema extends BaseSchema {
  compName: 'text'
  props?: TextProps
}

export class Text extends AbstractSchema {
  component: string = 'Text'
  name: string = '文本'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return '<span class="text-element" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" style="font-size: {{ $data["props"]->fontSize ?? "14px" }}; color: {{ $data["props"]->color ?? "#333" }}; font-weight: {{ $data["props"]->fontWeight ?? "normal" }}; text-align: {{ $data["props"]->textAlign ?? "left" }}; line-height: {{ $data["props"]->lineHeight ?? "1.5" }}; font-family: {{ $data["props"]->fontFamily ?? "inherit" }}; text-decoration: {{ $data["props"]->textDecoration ?? "none" }};">{{ $data["props"]->content ?? "默认文本" }}</span>'
  }
}

export default new Text([
  {
    ref: 'content',
    label: '文本内容',
    format: 'string',
    default: '文本内容',
    bindable: true,
  },
  {
    ref: 'fontSize',
    label: '字体大小',
    format: 'string',
    default: '14px',
    bindable: true,
  },
  {
    ref: 'color',
    label: '文字颜色',
    format: 'string',
    default: '#333333',
    bindable: true,
  },
  {
    ref: 'fontWeight',
    label: '字体粗细',
    format: 'string',
    default: 'normal',
    bindable: true,
  },
  {
    ref: 'textAlign',
    label: '文本对齐',
    format: 'string',
    default: 'left',
    bindable: true,
  }
])

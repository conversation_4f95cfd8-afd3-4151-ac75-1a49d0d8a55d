import { AbstractSchema, type BaseSchema } from './index'


export interface ButtonProps {
  text?: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'
  size?: 'large' | 'medium' | 'small' | 'mini'
  icon?: string
  disabled?: boolean
  plain?: boolean
  round?: boolean
  loading?: boolean
  backgroundColor?: string
  textColor?: string
  borderColor?: string
  fontSize?: string | number
  padding?: string
  borderRadius?: string | number
}

export interface ButtonSchema extends BaseSchema {
  compName: 'button'
  props?: ButtonProps
}


export class Button extends AbstractSchema {
  component: string = 'button'
  
  name: string = '按钮'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  template?: string | undefined;
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
    this.template = this.getTemplatePHP()
  }

  getTemplatePHP(){
    return '<button class="button-element {{ $data["props"]->type ?? "primary" }} {{ $data["props"]->size ?? "medium" }} @if($data["props"]->plain ?? false) is-plain @endif @if($data["props"]->round ?? false) is-round @endif @if($data["props"]->disabled ?? false) is-disabled @endif" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" style="background-color: {{ $data["props"]->backgroundColor ?? "#409eff" }}; color: {{ $data["props"]->textColor ?? "#fff" }}; border-color: {{ $data["props"]->borderColor ?? "#409eff" }}; font-size: {{ $data["props"]->fontSize ?? "14px" }}; padding: {{ $data["props"]->padding ?? "12px 20px" }}; border-radius: {{ $data["props"]->borderRadius ?? "4px" }}; border: 1px solid; cursor: {{ ($data["props"]->disabled ?? false) ? "not-allowed" : "pointer" }}; opacity: {{ ($data["props"]->disabled ?? false) ? "0.6" : "1" }};" @if($data["props"]->disabled ?? false) disabled @endif>@if($data["props"]->icon ?? false)<i class="{{ $data["props"]->icon }}"></i> @endif{{ $data["props"]->text ?? "按钮" }}</button>'
  }

}

const button = new Button([
  {
    ref: 'text',
    label: '按钮文本',
    format: 'string',
    default: '按钮',
    bindable: true,
  },
  {
    ref: 'type',
    label: '按钮类型',
    format: 'string',
    default: 'primary',
    bindable: true,
  },
  {
    ref: 'size',
    label: '按钮尺寸',
    format: 'string',
    default: 'medium',
    bindable: true,
  },
  {
    ref: 'disabled',
    label: '是否禁用',
    format: 'boolean',
    default: false,
    bindable: true,
  },
  {
    ref: 'backgroundColor',
    label: '背景颜色',
    format: 'string',
    default: '#409eff',
    bindable: true,
  },
  {
    ref: 'textColor',
    label: '文字颜色',
    format: 'string',
    default: '#ffffff',
    bindable: true,
  },
  {
    ref: 'borderRadius',
    label: '圆角大小',
    format: 'string',
    default: '4px',
    bindable: true,
  }
])
export default button
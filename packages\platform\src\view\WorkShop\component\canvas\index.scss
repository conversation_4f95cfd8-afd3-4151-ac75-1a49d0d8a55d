.lc-canvas {
  min-height: 400px;
  width: 100%;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    border-color: #007bff;
    background: #f8f9ff;
  }

  &.drag-over {
    border-color: #007bff;
    background: rgba(0, 123, 255, 0.05);
    border-style: solid;
  }

  // 空状态提示
  &:empty::before {
    content: "拖拽组件到此处或双击添加组件";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 14px;
    pointer-events: none;
  }
}

// 组件样式
div[data-id] {
  border: 1px solid #e5e5e5;
  padding: 10px;
  margin: 5px;
  border-radius: 4px;
  background: white;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
  }

  // 组件标识
  &::after {
    content: attr(data-id);
    position: absolute;
    top: -8px;
    left: 8px;
    background: #007bff;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
  }

  &:hover::after {
    opacity: 1;
  }
}

// 按钮组件样式
button[data-id] {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
  transition: all 0.2s ease;

  &:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}
import AbstractSchema, { type BaseSchema } from './index'


export interface ButtonProps {
  type: string
  size?: string
  icon?: string
  disabled?: boolean
  plain?: boolean
  autofocus?: boolean
  round?: boolean
}

export interface ButtonSchema extends BaseSchema {
  compName: 'button'
  props?: ButtonProps
}


export class Button extends AbstractSchema {
  component: string = 'Button'
  name: string = '按钮'
  reactive = false
  dataId = ''
  class = ''
  style = ''
  props = []
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return  `<{{props.type}} class='text-element' data-id='{{id}}'>{{props.text}}</{{props.type}}>`
  }

}

export default new Button([
  {
    ref: 'title',
    label: '标题',
    format: 'string',
    default: '按钮',
    bindable: true,

  }])

import React, { useState } from 'react'
import './SideMenu.scss'

export interface MenuItem {
  id: string
  name: string
  icon: string
  children?: SubMenuItem[]
}

export interface SubMenuItem {
  id: string
  name: string
  englishName: string
  icon?: string
}

const menuData: MenuItem[] = [
  {
    id: 'module',
    name: '模块',
    icon: '🧩',
    children: [
      { id: 'page-management', name: '页面管理', englishName: 'Page Management' },
      { id: 'app-page', name: '应用页面', englishName: 'App Page' },
      { id: 'system-page', name: '系统页面', englishName: 'System Page' }
    ]
  },
  {
    id: 'page',
    name: '页面',
    icon: '📄'
  },
  {
    id: 'style',
    name: '风格',
    icon: '🎨'
  },
  {
    id: 'app',
    name: '应用',
    icon: '📱'
  },
  {
    id: 'backend',
    name: '后台',
    icon: '💻'
  },
  {
    id: 'layer',
    name: '层级',
    icon: '⚙️'
  },
  {
    id: 'website',
    name: '网站版',
    icon: '🌐'
  },
  {
    id: 'recycle',
    name: '回收站',
    icon: '🗑️'
  },
  {
    id: 'code',
    name: '代码',
    icon: '</>'
  },
  {
    id: 'personal',
    name: '个人',
    icon: '👤'
  }
]

interface SideMenuProps {
  onMenuSelect?: (menuId: string, subMenuId?: string) => void
}

export const SideMenu: React.FC<SideMenuProps> = ({ onMenuSelect }) => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null)
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null)

  const handleMenuClick = (menuId: string) => {
    const menu = menuData.find(m => m.id === menuId)
    
    if (menu?.children) {
      // 有子菜单的情况
      if (expandedMenu === menuId) {
        setExpandedMenu(null)
      } else {
        setExpandedMenu(menuId)
      }
    } else {
      // 没有子菜单的情况
      setActiveMenu(menuId)
      setExpandedMenu(null)
      onMenuSelect?.(menuId)
    }
  }

  const handleSubMenuClick = (menuId: string, subMenuId: string) => {
    setActiveMenu(subMenuId)
    onMenuSelect?.(menuId, subMenuId)
  }

  return (
    <div className="side-menu">
      <div className="menu-list">
        {menuData.map((menu) => (
          <div key={menu.id} className="menu-item-container">
            <div
              className={`menu-item ${activeMenu === menu.id ? 'active' : ''} ${expandedMenu === menu.id ? 'expanded' : ''}`}
              onClick={() => handleMenuClick(menu.id)}
            >
              <div className="menu-icon">{menu.icon}</div>
              <div className="menu-name">{menu.name}</div>
              {menu.children && (
                <div className={`expand-arrow ${expandedMenu === menu.id ? 'rotated' : ''}`}>
                  ▼
                </div>
              )}
            </div>
            
            {/* 展开的子菜单浮层 */}
            {menu.children && expandedMenu === menu.id && (
              <div className="submenu-overlay">
                <div className="submenu-header">
                  <span className="submenu-title">{menu.name}管理</span>
                  <button 
                    className="close-btn"
                    onClick={(e) => {
                      e.stopPropagation()
                      setExpandedMenu(null)
                    }}
                  >
                    ✕
                  </button>
                </div>
                
                <div className="submenu-tabs">
                  <div className="tab active">应用页面</div>
                  <div className="tab">系统页面</div>
                </div>
                
                <div className="submenu-search">
                  <input 
                    type="text" 
                    placeholder="请输入搜索内容"
                    className="search-input"
                  />
                </div>
                
                <div className="submenu-list">
                  {menu.children.map((subMenu) => (
                    <div
                      key={subMenu.id}
                      className={`submenu-item ${activeMenu === subMenu.id ? 'active' : ''}`}
                      onClick={() => handleSubMenuClick(menu.id, subMenu.id)}
                    >
                      <div className="submenu-icon">🏠</div>
                      <div className="submenu-content">
                        <div className="submenu-name">{subMenu.name}</div>
                        <div className="submenu-english">{subMenu.englishName}</div>
                      </div>
                      <div className="submenu-actions">
                        <button className="action-btn">⚙️</button>
                        <button className="action-btn">📋</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default SideMenu

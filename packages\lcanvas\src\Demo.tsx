import { useState } from 'react'
import { AbsoluteSection, FlowSection } from './index'
import Tree, { type TreeNode } from './view/WorkShop/component/structTree/Tree'
import type { BaseSchema } from 'nui'

export default () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'div',
    id: '1',
    body: {
      'default': {
        component: 'div',
        id: '2222'
      }
    }
  })

  const [canvasMode, setCanvasMode] = useState<'absolute' | 'flow'>('absolute')
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null)

  // 树形数据
  const treeData: TreeNode[] = [
    {
      id: 1,
      label: '根节点',
      children: [
        {
          id: 2,
          label: '子节点 1',
          children: [
            { id: 3, label: '子节点 1-1' },
            { id: 4, label: '子节点 1-2' }
          ]
        },
        {
          id: 5,
          label: '子节点 2',
          children: [
            { id: 6, label: '子节点 2-1' },
            { id: 7, label: '子节点 2-2' }
          ]
        }
      ]
    }
  ]

  const handleSchemaChange = (newSchema: BaseSchema) => {
    setSchema(newSchema)
    console.log('Schema updated:', newSchema)
  }

  const handleAddComponent = (componentType: string, componentName: string) => {
    const newComponentId = `${componentType}-${Date.now()}`
    const newComponent: BaseSchema = {
      component: componentType,
      id: newComponentId,
      name: componentName
    }

    // 更新 schema，添加新组件到 body 中
    const updatedSchema = {
      ...schema,
      body: {
        ...schema.body,
        [newComponentId]: newComponent
      }
    }

    console.log('添加组件:', componentType, componentName)
    console.log('更新前 schema:', schema)
    console.log('更新后 schema:', updatedSchema)
    setSchema(updatedSchema)

    // 关闭模块面板
    setExpandedMenu(null)
  }

  const handleClearCanvas = () => {
    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
      setSchema({
        component: 'div',
        id: '1',
        body: {}
      })
    }
  }

  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* 左侧面板 */}
      <div style={{
        width: '300px',
        background: '#f8f9fa',
        borderRight: '1px solid #e5e5e5',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* 工具栏 */}
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #e5e5e5',
          background: 'white'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: '600' }}>
            页面设计器
          </h3>
          
          {/* 画布模式切换 */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>画布模式</div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                style={{
                  flex: 1,
                  padding: '8px 12px',
                  border: canvasMode === 'absolute' ? '2px solid #007bff' : '1px solid #ddd',
                  borderRadius: '6px',
                  background: canvasMode === 'absolute' ? '#e7f3ff' : 'white',
                  color: canvasMode === 'absolute' ? '#007bff' : '#333',
                  cursor: 'pointer',
                  fontSize: '12px',
                  fontWeight: canvasMode === 'absolute' ? '600' : 'normal'
                }}
                onClick={() => setCanvasMode('absolute')}
              >
                绝对定位
              </button>
              <button
                style={{
                  flex: 1,
                  padding: '8px 12px',
                  border: canvasMode === 'flow' ? '2px solid #007bff' : '1px solid #ddd',
                  borderRadius: '6px',
                  background: canvasMode === 'flow' ? '#e7f3ff' : 'white',
                  color: canvasMode === 'flow' ? '#007bff' : '#333',
                  cursor: 'pointer',
                  fontSize: '12px',
                  fontWeight: canvasMode === 'flow' ? '600' : 'normal'
                }}
                onClick={() => setCanvasMode('flow')}
              >
                流式布局
              </button>
            </div>
          </div>

          {/* 操作按钮 */}
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              style={{
                flex: 1,
                padding: '8px 12px',
                border: '1px solid #28a745',
                borderRadius: '6px',
                background: '#28a745',
                color: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }}
              onClick={() => console.log('导出 Schema:', schema)}
            >
              导出
            </button>
            <button
              style={{
                flex: 1,
                padding: '8px 12px',
                border: '1px solid #dc3545',
                borderRadius: '6px',
                background: 'white',
                color: '#dc3545',
                cursor: 'pointer',
                fontSize: '12px'
              }}
              onClick={handleClearCanvas}
            >
              清空
            </button>
          </div>
        </div>

        {/* 菜单区域 */}
        <div style={{ flex: 1, overflow: 'auto' }}>
          {/* 模块菜单 */}
          <div style={{ borderBottom: '1px solid #e5e5e5' }}>
            <div
              style={{
                padding: '16px',
                cursor: 'pointer',
                background: expandedMenu === 'module' ? '#e7f3ff' : 'transparent',
                borderLeft: expandedMenu === 'module' ? '3px solid #007bff' : '3px solid transparent',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
              onClick={() => setExpandedMenu(expandedMenu === 'module' ? null : 'module')}
            >
              <span style={{ fontWeight: '500', fontSize: '14px' }}>🧩 模块</span>
              <span style={{ fontSize: '12px', color: '#666' }}>
                {expandedMenu === 'module' ? '▼' : '▶'}
              </span>
            </div>
            
            {expandedMenu === 'module' && (
              <div style={{ padding: '16px', background: '#f8f9fa' }}>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>组件库</h4>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                  {[
                    { type: 'text', name: '文字', icon: '📝' },
                    { type: 'button', name: '按钮', icon: '🔘' },
                    { type: 'input', name: '输入框', icon: '📝' },
                    { type: 'image', name: '图片', icon: '🖼️' },
                    { type: 'div', name: '容器', icon: '📦' },
                    { type: 'card', name: '卡片', icon: '🃏' }
                  ].map(({ type, name, icon }) => (
                    <div
                      key={type}
                      style={{
                        padding: '12px',
                        border: '1px solid #e5e5e5',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        background: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        transition: 'all 0.2s ease'
                      }}
                      onClick={() => handleAddComponent(type, name)}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.borderColor = '#007bff'
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.borderColor = '#e5e5e5'
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                    >
                      <span style={{ fontSize: '20px' }}>{icon}</span>
                      <div>
                        <div style={{ fontWeight: '500', fontSize: '14px' }}>{name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{type}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 结构菜单 */}
          <div style={{ borderBottom: '1px solid #e5e5e5' }}>
            <div
              style={{
                padding: '16px',
                cursor: 'pointer',
                background: expandedMenu === 'structure' ? '#e7f3ff' : 'transparent',
                borderLeft: expandedMenu === 'structure' ? '3px solid #007bff' : '3px solid transparent',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
              onClick={() => setExpandedMenu(expandedMenu === 'structure' ? null : 'structure')}
            >
              <span style={{ fontWeight: '500', fontSize: '14px' }}>🌳 结构</span>
              <span style={{ fontSize: '12px', color: '#666' }}>
                {expandedMenu === 'structure' ? '▼' : '▶'}
              </span>
            </div>
            
            {expandedMenu === 'structure' && (
              <div style={{ padding: '16px', background: '#f8f9fa' }}>
                <Tree
                  data={treeData}
                  defaultExpandAll={true}
                  showCheckbox={false}
                  onNodeClick={(node) => console.log('点击节点:', node)}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 中间画布区域 */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* 画布工具栏 */}
        <div style={{
          padding: '12px 20px',
          background: 'white',
          borderBottom: '1px solid #e5e5e5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <span style={{ fontSize: '14px', fontWeight: '500' }}>
              {canvasMode === 'absolute' ? '绝对定位画布' : '流式布局画布'}
            </span>
            <span style={{
              background: '#007bff',
              color: 'white',
              fontSize: '10px',
              padding: '2px 6px',
              borderRadius: '10px'
            }}>
              {Object.keys(schema.body || {}).length} 个组件
            </span>
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <button style={{
              padding: '6px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              预览
            </button>
            <button style={{
              padding: '6px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              撤销
            </button>
            <button style={{
              padding: '6px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              重做
            </button>
          </div>
        </div>

        {/* 画布内容 */}
        <div style={{ flex: 1, padding: '20px', background: '#f5f5f5' }}>
          {canvasMode === 'absolute' ? (
            <AbsoluteSection
              schema={schema}
              width="100%"
              height="500px"
              onSchemaChange={handleSchemaChange}
            />
          ) : (
            <FlowSection
              schema={schema}
              width="100%"
              height="500px"
              onSchemaChange={handleSchemaChange}
            />
          )}
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div style={{
        width: '300px',
        background: '#f8f9fa',
        borderLeft: '1px solid #e5e5e5',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #e5e5e5',
          background: 'white'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '16px', fontWeight: '600' }}>
            属性面板
          </h3>
          
          <div style={{ marginBottom: '16px' }}>
            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>组件统计</div>
            {schema.body && Object.keys(schema.body).length > 0 ? (
              <div>
                {Object.entries(
                  Object.values(schema.body).reduce((acc: Record<string, number>, item) => {
                    acc[item.component] = (acc[item.component] || 0) + 1
                    return acc
                  }, {})
                ).map(([component, count]) => (
                  <div key={component} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '4px 0',
                    fontSize: '12px'
                  }}>
                    <span>{component}</span>
                    <span style={{ color: '#666' }}>{count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ fontSize: '12px', color: '#999' }}>暂无组件</div>
            )}
          </div>

          <div style={{ display: 'flex', gap: '8px' }}>
            <button style={{
              flex: 1,
              padding: '8px 12px',
              border: '1px solid #007bff',
              borderRadius: '6px',
              background: 'white',
              color: '#007bff',
              cursor: 'pointer',
              fontSize: '12px'
            }}>
              重置
            </button>
          </div>
        </div>

        <div style={{ flex: 1, padding: '16px', overflow: 'auto' }}>
          <h4 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>Schema 结构</h4>
          <pre style={{
            background: '#f8f9fa',
            padding: '12px',
            borderRadius: '6px',
            fontSize: '11px',
            lineHeight: '1.4',
            overflow: 'auto',
            border: '1px solid #e5e5e5',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word'
          }}>
            {JSON.stringify(schema, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  )
}

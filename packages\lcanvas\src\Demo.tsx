import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';
import { FlowSection } from './ndesignUI/container/FlowSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import { useEffect, useState } from 'react';
import DemoWasm from './demo/demoWasm.tsx';
import axios from 'axios';

const examleSchema:BaseSchema = {
  component:'div',
  id:'1',
  // template:`<?php
  // $bladeString = '@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif';
  // #$bladeString = '<div> @customFunction("hello") </div>';
  // $stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
  // $temp = getBladeCode($bladeString);
  // echo "template===$temp";

  // echo $stringOutput;
  // ?>`
  template:`@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif`

}

const absoluteSectionSchema:BaseSchema = {
  component:'AbsoluteSection',
  id:'absolute-section-demo',
  name: '绝对定位容器演示',
  body: {
    'btn1': {
      component: 'button',
      id: 'btn1',
      name: '按钮1'
    },
    'btn2': {
      component: 'button',
      id: 'btn2',
      name: '按钮2'
    }
  }
}

const flowSectionSchema:BaseSchema = {
  component:'FlowSection',
  id:'flow-section-demo',
  name: '流式布局容器演示',
  body: {
    'btn1': {
      component: 'button',
      id: 'btn1',
      name: '主要按钮'
    },
    'btn2': {
      component: 'button',
      id: 'btn2',
      name: '次要按钮'
    },
    'text1': {
      component: 'text',
      id: 'text1',
      name: '这是一段文本内容'
    },
    'div1': {
      component: 'div',
      id: 'div1',
      name: '容器块'
    }
  }
}

function App() {
  const [scheme, setSchema] = useState<BaseSchema>(examleSchema)
  const [absoluteSchema, setAbsoluteSchema] = useState<BaseSchema>(absoluteSectionSchema)
  const [flowSchema, setFlowSchema] = useState<BaseSchema>(flowSectionSchema)
  const [loaded, setLoaded] = useState(false)
  useEffect(()=>{
    (async function(){
      let render = BladeRender.getInstance();
      await render.defineVariable("isAdmin","0")
      setSchema(examleSchema);

    })()
  },[])
  useEffect(()=>{
    (async function(){
      if( scheme ){
        setSchema(scheme)
      }
      try {
        const response = await axios.get('http://huitui.yidaba.com/test/lib.zip', {
          responseType: 'blob', // 设置响应类型为 blob，用于处理二进制文件
        });
        setLoaded(true)
      } catch (error) {
        console.error('下载文件失败:', error);
        throw error;
      }
    })();
  },[])

  const handleAbsoluteSchemaChange = (newSchema: BaseSchema) => {
    setAbsoluteSchema(newSchema)
    console.log('AbsoluteSection Schema 更新:', newSchema)
  }

  const handleFlowSchemaChange = (newSchema: BaseSchema) => {
    setFlowSchema(newSchema)
    console.log('FlowSection Schema 更新:', newSchema)
  }

  return (
    <>
      <div style={{ padding: '20px' }}>
        { !loaded && <Loading></Loading> }
        <h2>LCanvas 演示</h2>
        {/* <DemoWasm></DemoWasm> */}
        <div style={{ marginBottom: '30px' }}>
          <h3>BladeView 演示</h3>
          <BladeView schema={scheme}></BladeView>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3>AbsoluteSection 演示</h3>
          <p>双击容器添加按钮，Shift+双击添加容器，Ctrl+双击添加嵌套容器，拖拽移动位置</p>
          <AbsoluteSection
            schema={absoluteSchema}
            width="600px"
            height="400px"
            onSchemaChange={handleAbsoluteSchemaChange}
          />
        </div>

        <div style={{ marginBottom: '30px' }}>
          <h3>FlowSection 演示</h3>
          <p>双击容器添加按钮，Shift+双击添加文本，Ctrl+双击添加容器，拖拽组件到容器中</p>
          <FlowSection
            schema={flowSchema}
            width="600px"
            height="300px"
            direction="row"
            wrap={true}
            gap={12}
            rowGap={8}
            columnGap={12}
            justify="flex-start"
            align="flex-start"
            onSchemaChange={handleFlowSchemaChange}
          />
        </div>
      </div>
    </>
  )
}

export default App

.module-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.module-selector {
  width: 600px;
  max-width: 90vw;
  height: 500px;
  max-height: 80vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;
    background: #f8f9fa;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #333;
      font-weight: 600;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: #666;
      padding: 4px;
      border-radius: 4px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #e9ecef;
      }
    }
  }

  .module-search {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;

    .search-input {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
      transition: border-color 0.2s ease;

      &:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }

      &::placeholder {
        color: #999;
      }
    }
  }

  .module-categories {
    display: flex;
    padding: 0 20px;
    border-bottom: 1px solid #e5e5e5;
    background: #f8f9fa;

    .category-btn {
      background: none;
      border: none;
      padding: 12px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        color: #333;
        background: rgba(0, 123, 255, 0.05);
      }

      &.active {
        color: #007bff;
        border-bottom-color: #007bff;
        background: rgba(0, 123, 255, 0.05);
      }
    }
  }

  .module-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    align-content: start;

    .module-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: white;

      &:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }

      .module-icon {
        font-size: 24px;
        margin-right: 12px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 6px;
      }

      .module-info {
        flex: 1;

        .module-name {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
        }

        .module-description {
          font-size: 12px;
          color: #666;
          line-height: 1.3;
        }
      }
    }
  }

  .no-modules {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;

    p {
      margin: 0;
    }
  }
}

// 拖拽状态样式
.module-item {
  &[draggable="true"] {
    user-select: none;

    &:hover::after {
      content: "拖拽到画布";
      position: absolute;
      top: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: #333;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 1000;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .module-selector {
    width: 95vw;
    height: 70vh;

    .module-list {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .module-categories {
      flex-wrap: wrap;
      
      .category-btn {
        padding: 8px 12px;
        font-size: 13px;
      }
    }
  }
}

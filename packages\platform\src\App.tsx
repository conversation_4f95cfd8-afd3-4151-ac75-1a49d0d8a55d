import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import DemoWasm from './demo/demoWasm.tsx'
import Home from './view/home/<USER>'
import Designer  from './view/WorkShop/Designer.tsx';
import DesignerSimple from './view/WorkShop/DesignerSimple.tsx';
import GridSection from './ndesignUI/container/GridSection.tsx'


import './App.scss'

function App() {
  return (
    <>
      <Router>
        <Routes>
          {/* 默认显示简化版 Designer 界面 */}
          <Route path="/" element={<DesignerSimple />} />
          <Route path="/home" element={<Home />} />
          <Route path="/comp" element={<GridSection />} />
          <Route path="/dx" element={<Designer />} />
          <Route path="/simple" element={<DesignerSimple />} />
          <Route path="/wasm" element={<DemoWasm />} />
        </Routes>
      </Router>
    </>
  )
}

export default App

import React from 'react';
import './Loading.scss';

// 定义 Loading 组件的 Props 类型
type LoadingProps = {
  size?: 'small' | 'medium' | 'large';
  color?: string;
};

const Loading: React.FC<LoadingProps> = ({ size = 'medium', color = '#000' }) => {
  // 根据不同尺寸设置类名
  let sizeClass = '';
  switch (size) {
    case 'small':
      sizeClass = 'loading-small';
      break;
    case 'medium':
      sizeClass = 'loading-medium';
      break;
    case 'large':
      sizeClass = 'loading-large';
      break;
    default:
      sizeClass = 'loading-medium';
  }

  return (
    <div 
      className={`loading-spinner ${sizeClass}`} 
      style={{ borderTopColor: color }}
    ></div>
  );
};

export default Loading;
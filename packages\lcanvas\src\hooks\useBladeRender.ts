import { useState, useEffect, useCallback } from 'react';
import { BladeRender } from '../core/render/BladeRender';
import { WASMCacheManager } from '../core/cache/WASMCacheManager';

/**
 * BladeRender 状态
 */
export interface BladeRenderState {
  isReady: boolean;
  isLoading: boolean;
  error: Error | null;
  cacheInfo: any;
}

/**
 * BladeRender Hook 返回值
 */
export interface UseBladeRenderReturn extends BladeRenderState {
  render: BladeRender | null;
  ensureReady: () => Promise<void>;
  clearCache: () => void;
  forceRefresh: () => Promise<void>;
  getCacheInfo: () => any;
}

/**
 * 使用 BladeRender 的 React Hook
 * 提供 WASM 状态管理、缓存控制等功能
 */
export const useBladeRender = (): UseBladeRenderReturn => {
  const [state, setState] = useState<BladeRenderState>({
    isReady: BladeRender.isWASMReady,
    isLoading: false,
    error: null,
    cacheInfo: null
  });

  const [render] = useState(() => BladeRender.getInstance());

  /**
   * 更新状态
   */
  const updateState = useCallback((updates: Partial<BladeRenderState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 确保 WASM 已准备就绪
   */
  const ensureReady = useCallback(async () => {
    if (BladeRender.isWASMReady) {
      updateState({ isReady: true });
      return;
    }

    if (state.isLoading) {
      // 如果正在加载，等待加载完成
      return new Promise<void>((resolve, reject) => {
        const checkReady = () => {
          if (BladeRender.isWASMReady) {
            updateState({ isReady: true, isLoading: false });
            resolve();
          } else if (!state.isLoading) {
            // 加载失败
            reject(state.error || new Error('WASM 加载失败'));
          } else {
            // 继续等待
            setTimeout(checkReady, 100);
          }
        };
        checkReady();
      });
    }

    updateState({ isLoading: true, error: null });

    try {
      await render.loadWASM();
      updateState({ 
        isReady: true, 
        isLoading: false, 
        cacheInfo: WASMCacheManager.getCacheInfo() 
      });
    } catch (error) {
      const err = error instanceof Error ? error : new Error('WASM 加载失败');
      updateState({ 
        isReady: false, 
        isLoading: false, 
        error: err 
      });
      throw err;
    }
  }, [render, state.isLoading, state.error, updateState]);

  /**
   * 清理缓存
   */
  const clearCache = useCallback(() => {
    BladeRender.clearCache();
    updateState({ 
      isReady: false, 
      cacheInfo: null 
    });
  }, [updateState]);

  /**
   * 强制刷新（清理缓存并重新加载）
   */
  const forceRefresh = useCallback(async () => {
    BladeRender.forceRefresh();
    updateState({ 
      isReady: false, 
      isLoading: false, 
      error: null, 
      cacheInfo: null 
    });
    await ensureReady();
  }, [ensureReady, updateState]);

  /**
   * 获取缓存信息
   */
  const getCacheInfo = useCallback(() => {
    const info = BladeRender.getCacheInfo();
    updateState({ cacheInfo: info });
    return info;
  }, [updateState]);

  /**
   * 初始化时检查状态
   */
  useEffect(() => {
    const initState = {
      isReady: BladeRender.isWASMReady,
      cacheInfo: WASMCacheManager.getCacheInfo()
    };
    updateState(initState);
  }, [updateState]);

  return {
    ...state,
    render,
    ensureReady,
    clearCache,
    forceRefresh,
    getCacheInfo
  };
};

/**
 * 简化版 Hook，只关心是否准备就绪
 */
export const useBladeRenderReady = () => {
  const { isReady, isLoading, error, ensureReady } = useBladeRender();
  
  useEffect(() => {
    if (!isReady && !isLoading && !error) {
      ensureReady().catch(console.error);
    }
  }, [isReady, isLoading, error, ensureReady]);

  return { isReady, isLoading, error };
};

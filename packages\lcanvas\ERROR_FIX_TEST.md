# WASM 错误修复测试指南

## 🐛 原始错误

```
Uncaught (in promise) TypeError: Cannot read properties of null (reading 'addEventListener')
    at BladeRender.ts:281:13
```

## 🔧 修复内容

1. **问题分析**: 从缓存恢复 WASM 状态时，`BladeRender.isWASMReady` 被设置为 `true`，但 `this.php` 实例为 `null`，导致调用 `addEventListener` 时出错。

2. **修复方案**: 
   - 改进了 `executePhp` 方法，确保在使用 PHP 实例前检查其存在性
   - 增强了 `useBladeRender` Hook，添加了重试机制和更好的错误处理
   - 暂时禁用了缓存恢复功能，确保每次都正确创建 PHP 实例

## 🧪 测试步骤

### 1. 测试简化版组件（推荐先测试）

访问: `/comp-simple`

这个页面使用传统的加载方式，应该能够正常工作：
- ✅ 显示加载状态
- ✅ 成功加载 WASM
- ✅ 显示绿色成功消息

### 2. 测试优化版组件

访问: `/comp`

这个页面使用新的 Hook 和缓存系统：
- ✅ 显示缓存调试面板（右上角）
- ✅ 显示加载状态
- ✅ 成功加载 WASM
- ✅ 显示 WASM 状态信息

### 3. 测试缓存功能

访问: `/cache-test`

这个页面提供完整的缓存测试：
- ✅ 显示详细的加载信息
- ✅ 提供缓存操作按钮
- ✅ 记录加载时间

## 🔍 验证要点

### 成功标志
1. **无控制台错误**: 不应该再看到 `addEventListener` 错误
2. **正常渲染**: 页面能够显示 BladeView 内容
3. **PHP 实例存在**: 在控制台中 `BladeRender.getInstance().php` 不为 null

### 调试信息
打开浏览器控制台，应该看到类似信息：
```
开始加载 WASM...
load...
host http://localhost:5173
zip....
类名：ZipArchive
after extract....
WASM 加载完成，PHP 实例已创建
```

### 错误处理
如果仍然出现错误：
1. 刷新页面重试
2. 清理浏览器缓存
3. 检查网络连接
4. 确认 WASM 资源文件可访问

## 🛠️ 技术细节

### 修复的关键代码

**executePhp 方法改进**:
```typescript
// 确保 PHP 实例存在
if (BladeRender.isWASMReady && this.php) {
  php = this.php;
  php.addEventListener('output', this.onBladeOutput);
  await php.run(code);
} else {
  // 如果 WASM 未就绪或 PHP 实例不存在，重新加载
  await this.loadWASM();
  php = this.php;
  if (php) {
    php.addEventListener('output', this.onBladeOutput);
    await php.run(code);
  } else {
    throw new Error('PHP 实例创建失败');
  }
}
```

**useBladeRender Hook 改进**:
```typescript
// 强制重新加载以确保 PHP 实例正确创建
BladeRender.isWASMReady = false;
render.php = null;

await render.loadWASM();

// 验证 PHP 实例是否正确创建
if (!render.php) {
  throw new Error('PHP 实例创建失败');
}
```

## 📊 测试结果记录

请在测试后填写：

- [ ] `/comp-simple` 页面正常加载
- [ ] `/comp` 页面正常加载  
- [ ] `/cache-test` 页面正常加载
- [ ] 无控制台错误
- [ ] BladeView 内容正确显示
- [ ] 缓存调试面板工作正常

## 🚀 下一步

如果测试通过：
1. 可以继续优化缓存策略
2. 考虑重新启用智能缓存恢复
3. 添加更多的错误处理和用户反馈

如果测试失败：
1. 检查具体错误信息
2. 尝试使用简化版组件
3. 考虑回退到更保守的实现方案

import { useState, useEffect, useCallback } from 'react'
import { AbsoluteSection, FlowSection, BladeView, BladeRender, CacheDebugPanel } from 'lcanvas'
import Tree, { type TreeNode } from './component/structTree/Tree'
import type { BaseSchema } from 'nui'
import { getDefinedComponent, definedComponents } from 'nui'

// 获取可用的 NUI 组件列表
const getAvailableComponents = () => {
  const components = [];
  for (const [key, component] of Object.entries(definedComponents)) {
    components.push({
      type: key,
      name: component.name || key, // 确保 name 不为空
      icon: getComponentIcon(key)
    });
  }
  return components;
};

// 根据组件类型获取图标
const getComponentIcon = (componentType: string) => {
  const iconMap: Record<string, string> = {
    'text': '📝',
    'button': '🔘',
    'image': '🖼️',
    'icon': '🎨',
    'container': '📦',
    'div': '📦',
    'AbsoluteSection': '🎯',
    'FlowSection': '🌊'
  };
  return iconMap[componentType] || '🧩';
};

// 获取容器组件列表
const getContainerComponents = () => {
  return [
    {
      type: 'AbsoluteSection',
      name: '绝对定位容器',
      icon: '🎯',
      description: '拖拽容器'
    },
    {
      type: 'FlowSection',
      name: '流式布局容器',
      icon: '🌊',
      description: '自动排列容器'
    }
  ];
};

export default () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'div',
    id: '1',
    template: '<div style="padding: 20px; min-height: 400px; background: #f8f9fa; border-radius: 8px;"><slot></slot></div>',
    body: []
  })

  const [expandedMenu, setExpandedMenu] = useState<string | null>(null)
  // 移除 canvasMode 状态，统一使用 LCanvas 模式
  // const [canvasMode, setCanvasMode] = useState<'lcanvas' | 'absolute' | 'flow'>('lcanvas')
  const [isAddingComponent, setIsAddingComponent] = useState(false)
  
  // 添加组件投放状态
  const [pendingComponent, setPendingComponent] = useState<{
    type: string;
    name: string;
  } | null>(null);

  // 页面树数据
  const [pageTreeData] = useState<TreeNode[]>([
    {
      id: 'app-pages',
      label: '应用页面',
      children: [
        {
          id: 'home',
          label: '首页',
          icon: '🏠',
          type: 'page',
          path: '/home',
          status: 'published'
        },
        {
          id: 'about',
          label: '关于我们',
          icon: '📋',
          type: 'page',
          path: '/about',
          status: 'draft'
        },
        {
          id: 'products',
          label: '产品中心',
          icon: '📦',
          type: 'page',
          path: '/products',
          status: 'published',
          children: [
            {
              id: 'product-list',
              label: '产品列表',
              icon: '📝',
              type: 'page',
              path: '/products/list',
              status: 'published'
            },
            {
              id: 'product-detail',
              label: '产品详情',
              icon: '🔍',
              type: 'page',
              path: '/products/detail',
              status: 'published'
            }
          ]
        },
        {
          id: 'contact',
          label: '联系我们',
          icon: '📞',
          type: 'page',
          path: '/contact',
          status: 'published'
        }
      ]
    },
    {
      id: 'system-pages',
      label: '系统页面',
      children: [
        {
          id: 'login',
          label: '登录页',
          icon: '🔐',
          type: 'system',
          path: '/login',
          status: 'published'
        },
        {
          id: 'register',
          label: '注册页',
          icon: '📝',
          type: 'system',
          path: '/register',
          status: 'published'
        },
        {
          id: '404',
          label: '404错误页',
          icon: '❌',
          type: 'system',
          path: '/404',
          status: 'published'
        },
        {
          id: 'admin',
          label: '管理后台',
          icon: '⚙️',
          type: 'system',
          path: '/admin',
          status: 'published',
          children: [
            {
              id: 'admin-dashboard',
              label: '仪表盘',
              icon: '📊',
              type: 'system',
              path: '/admin/dashboard',
              status: 'published'
            },
            {
              id: 'admin-users',
              label: '用户管理',
              icon: '👥',
              type: 'system',
              path: '/admin/users',
              status: 'published'
            },
            {
              id: 'admin-settings',
              label: '系统设置',
              icon: '⚙️',
              type: 'system',
              path: '/admin/settings',
              status: 'draft'
            }
          ]
        }
      ]
    }
  ])

  const [selectedPage, setSelectedPage] = useState<TreeNode | null>(null)
  const [pageSearchValue, setPageSearchValue] = useState('')

  const handleMenuClick = (menuId: string) => {
    if (expandedMenu === menuId) {
      setExpandedMenu(null)
    } else {
      setExpandedMenu(menuId)
    }
  }

  const handleSchemaChange = (newSchema: BaseSchema) => {
    console.log('Schema 更新前:', schema)
    console.log('Schema 更新后:', newSchema)
    setSchema(newSchema)
  }

  const handleAddComponent = useCallback((componentType: string, componentName: string) => {
    const callStack = new Error().stack;
    console.log('🎯 handleAddComponent 被调用:', {
      componentType,
      componentName,
      isAddingComponent,
      timestamp: Date.now(),
      callStack: callStack?.split('\n').slice(1, 4).join('\n') // 显示调用栈的前3行
    });

    // 防止重复添加
    if (isAddingComponent) {
      console.log('⚠️ 正在添加组件，忽略重复请求');
      return;
    }

    setIsAddingComponent(true);

    try {
      // 检查是否为容器组件
      if (componentType === 'AbsoluteSection' || componentType === 'FlowSection') {
        // 容器组件直接添加到根级别
        const newComponentId = `${componentType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
        console.log('🆔 生成容器组件 ID:', newComponentId);

        const newComponent = createLCanvasComponent(componentType, newComponentId, componentName);
        console.log('🔧 容器组件创建完成:', newComponent);

        // 更新 schema，添加新组件到 body 数组中
        const currentBody = Array.isArray(schema.body) ? schema.body : []
        const updatedSchema = {
          ...schema,
          template: '<div style="padding: 20px; min-height: 400px; background: #f8f9fa; border-radius: 8px;"><slot></slot></div>',
          body: [...currentBody, newComponent]
        }

        setSchema(updatedSchema)
        console.log('✅ 容器组件添加完成');
        
        // 关闭模块面板
        setExpandedMenu(null)
      } else {
        // 普通组件进入投放模式
        const { containers } = separateComponents();
        
        if (containers.length === 0) {
          // 如果没有容器，提示用户先添加容器
          alert('请先添加一个容器组件（绝对定位容器或流式布局容器）');
        } else {
          // 进入投放模式
          setPendingComponent({
            type: componentType,
            name: componentName
          });
          console.log('🎯 进入组件投放模式:', componentType);
          
          // 关闭模块面板，让用户选择容器
          setExpandedMenu(null)
        }
      }
    } finally {
      // 延迟重置状态，防止快速点击
      setTimeout(() => {
        console.log('🔓 重置 isAddingComponent 状态');
        setIsAddingComponent(false);
      }, 300);
    }
  }, [schema, isAddingComponent]) // 移除 canvasMode 依赖

  // 重置画布
  const handleResetCanvas = useCallback(() => {
    const cleanSchema = {
      component: 'div',
      id: '1',
      template: '<div style="padding: 20px; min-height: 400px; background: #f8f9fa; border-radius: 8px;"><slot></slot></div>',
      body: []
    };
    console.log('🔄 重置画布');
    setSchema(cleanSchema);
  }, []);

  // 创建 LCanvas 组件的辅助函数 - 使用 nui 包中的组件定义
  const createLCanvasComponent = (componentType: string, id: string, name: string): BaseSchema => {
    // 首先尝试从 nui 包获取组件定义
    const definedComponent = getDefinedComponent(componentType.toLowerCase());

    console.log(`🔍 查找组件: ${componentType}`, definedComponent ? '✅ 找到' : '❌ 未找到');

    if (definedComponent) {
      // 使用 nui 包中定义的组件模板
      console.log(`📝 使用 nui 包模板:`, definedComponent.template);

      // 根据组件类型设置默认的 props，这些 props 会被 PHP 模板使用
      let defaultProps: any = {};
      
      switch (componentType.toLowerCase()) {
        case 'button':
          defaultProps = {
            text: name,
            type: 'primary',
            size: 'medium',
            backgroundColor: '#409eff',
            textColor: '#ffffff',
            borderColor: '#409eff',
            borderRadius: '4px',
            fontSize: '14px',
            padding: '12px 20px',
            disabled: false,
            plain: false,
            round: false,
            dataId: id
          };
          break;
        case 'text':
          defaultProps = {
            content: name,
            fontSize: '14px',
            color: '#333333',
            fontWeight: 'normal',
            textAlign: 'left',
            lineHeight: '1.5',
            fontFamily: 'inherit',
            textDecoration: 'none',
            dataId: id
          };
          break;
        case 'icon':
          defaultProps = {
            name: 'star',
            size: '16px',
            color: '#333333',
            dataId: id
          };
          break;
        case 'image':
          defaultProps = {
            src: 'https://via.placeholder.com/150x100',
            alt: name,
            width: '150px',
            height: '100px',
            dataId: id
          };
          break;
        default:
          // 其他组件的默认属性
          defaultProps = {
            text: name,
            content: name,
            dataId: id
          };
      }

      return {
        ...definedComponent,
        id,
        name,
        // 设置 props 属性，这些会被 PHP 模板通过 $data["props"] 访问
        props: defaultProps,
        // 使用 NUI 组件的原始 PHP 模板
        template: definedComponent.template
      };
    }

    // 如果 nui 包中没有定义，则使用自定义模板（保留一些特殊组件）
    switch (componentType) {
      case 'card':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 15px; margin: 10px; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background: white;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${name}</h3>
            <p style="margin: 0 0 10px 0; color: #666;">这是一个卡片组件</p>
            @if($data['isAdmin'])
              <div style="color: #28a745; font-size: 12px;">管理员可见内容</div>
            @else
              <div style="color: #6c757d; font-size: 12px;">普通用户内容</div>
            @endif
          </div>`
        };

      case 'list':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <h4>${name}</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
              @foreach([1, 2, 3, 4, 5] as $item)
                <li style="margin: 5px 0;">列表项 {{ $item }}</li>
              @endforeach
            </ul>
          </div>`
        };

      case 'conditional':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 15px; margin: 10px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">${name} - 条件渲染</h4>
            @if($data['isAdmin'])
              <div style="padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">
                ✅ 管理员权限：您可以看到这个内容
              </div>
            @else
              <div style="padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24;">
                ❌ 普通用户：权限不足
              </div>
            @endif
          </div>`
        };

      case 'AbsoluteSection':
        return {
          component: 'AbsoluteSection',
          id,
          name,
          template: `<div class="absolute-section" style="position: relative; min-height: 200px; border: 2px dashed #17a2b8; border-radius: 8px; padding: 20px; margin: 10px 0; background: rgba(23, 162, 184, 0.05); display: block;">
            <div style="position: absolute; top: 10px; left: 10px; background: #17a2b8; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">🎯 绝对定位容器</div>
            <div style="margin-top: 30px; color: #666; text-align: center; font-size: 14px;">拖拽组件到这里</div>
            <slot></slot>
          </div>`,
          body: []
        };

      case 'FlowSection':
        return {
          component: 'FlowSection',
          id,
          name,
          template: `<div class="flow-section" style="display: block; min-height: 150px; border: 2px dashed #17a2b8; border-radius: 8px; padding: 20px; margin: 10px 0; background: rgba(23, 162, 184, 0.05);">
            <div style="background: #17a2b8; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-bottom: 15px; display: inline-block;">🌊 流式布局容器</div>
            <div style="color: #666; text-align: center; font-size: 14px; margin-bottom: 15px;">添加组件到这里，它们会自动排列</div>
            <div style="display: flex; flex-wrap: wrap; gap: 10px; min-height: 100px;">
              <slot></slot>
            </div>
          </div>`,
          body: []
        };

      default:
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <p>${name} - ${componentType}</p>
          </div>`
        };
    }
  }

  const handleExportSchema = () => {
    const dataStr = JSON.stringify(schema, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'schema.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleClearCanvas = () => {
    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
      setSchema({
        component: 'div',
        id: '1',
        template: '<div><slot></slot></div>',
        body: []
      })
    }
  }

  const handleResetPositions = () => {
    // 这个功能需要 AbsoluteSection 组件支持，暂时显示提示
    alert('重置位置功能需要在 AbsoluteSection 组件中实现')
  }

  const handlePageNodeClick = (node: TreeNode) => {
    setSelectedPage(node)
    console.log('选中页面:', node)
  }

  const handlePageSearchChange = (value: string) => {
    setPageSearchValue(value)
  }

  const renderPageNode = (node: TreeNode) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'published': return '#28a745'
        case 'draft': return '#ffc107'
        case 'archived': return '#6c757d'
        default: return '#6c757d'
      }
    }

    const getStatusText = (status: string) => {
      switch (status) {
        case 'published': return '已发布'
        case 'draft': return '草稿'
        case 'archived': return '已归档'
        default: return '未知'
      }
    }

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '14px' }}>{node.icon}</span>
          <span style={{ fontSize: '13px', color: '#333' }}>{node.label}</span>
          {node.path && (
            <span style={{
              fontSize: '11px',
              color: '#999',
              fontFamily: 'monospace'
            }}>
              {node.path}
            </span>
          )}
        </div>
        {node.status && (
          <span style={{
            fontSize: '10px',
            padding: '2px 6px',
            borderRadius: '10px',
            backgroundColor: getStatusColor(node.status),
            color: 'white'
          }}>
            {getStatusText(node.status)}
          </span>
        )}
      </div>
    )
  }

  // 为组件动态设置 props 并渲染的辅助函数
  const renderComponentWithProps = useCallback(async (componentSchema: BaseSchema): Promise<string> => {
    if (!componentSchema.template) return '';
    
    const render = BladeRender.getInstance();
    
    // 如果组件有 props，临时设置它们
    if (componentSchema.props) {
      console.log('🎯 为组件设置 props:', componentSchema.id, componentSchema.props);
      await render.defineVariable("props", componentSchema.props);
    }
    
    // 渲染组件模板
    const result = await render.executePhp(componentSchema.template);
    console.log('✅ 组件渲染完成:', componentSchema.id, result);
    
    return result;
  }, []);

  // 分离容器组件和普通组件
  const separateComponents = () => {
    if (!Array.isArray(schema.body)) return { containers: [], others: [] };
    
    const containers = schema.body.filter(item => 
      item.component === 'AbsoluteSection' || item.component === 'FlowSection'
    );
    const others = schema.body.filter(item => 
      item.component !== 'AbsoluteSection' && item.component !== 'FlowSection'
    );
    
    return { containers, others };
  };

  // 添加组件到指定容器的辅助函数
  const addComponentToContainer = (containerId: string, componentType: string, componentName: string) => {
    console.log('🎯 添加组件到容器:', { containerId, componentType, componentName });
    
    const newComponentId = `${componentType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // 使用 createLCanvasComponent 创建完整的 NUI 组件
    const newComponent = createLCanvasComponent(componentType, newComponentId, componentName);
    console.log('🔧 使用 NUI 组件定义创建:', newComponent);
    
    // 更新指定容器的 body
    const currentBody = Array.isArray(schema.body) ? schema.body : [];
    const updatedBody = currentBody.map((item: BaseSchema) => {
      if (item.id === containerId) {
        // 检查容器类型，使用不同的 body 格式
        if (item.component === 'FlowSection') {
          // FlowSection 使用对象格式 - 使用类型断言
          const containerBody = item.body && typeof item.body === 'object' && !Array.isArray(item.body) ? item.body as Record<string, BaseSchema> : {};
          return {
            ...item,
            body: {
              ...containerBody,
              [newComponentId]: newComponent
            } as any // 类型断言为 any 以绕过类型检查
          };
        } else {
          // AbsoluteSection 和其他容器使用数组格式
          const containerBody = Array.isArray(item.body) ? item.body : [];
          return {
            ...item,
            body: [...containerBody, newComponent]
          };
        }
      }
      return item;
    });
    
    setSchema({
      ...schema,
      body: updatedBody
    });
    
    // 清除投放状态
    setPendingComponent(null);
    console.log('✅ 组件投放完成，清除投放状态');
  };

  // 取消投放模式
  const cancelPendingComponent = () => {
    setPendingComponent(null);
    console.log('❌ 取消组件投放');
  };

  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      background: '#f5f5f5'
    }}>
      {/* 顶部测试链接 */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        zIndex: 1000
      }}>
      </div>
      {/* 左侧菜单 */}
      <div style={{
        width: '60px',
        background: 'linear-gradient(180deg, #ff6b6b 0%, #ee5a52 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '10px 0',
        position: 'relative',
        zIndex: 1000
      }}>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'module' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'module' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('module')}
        >
          🧩<br/>模块
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'page' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'page' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('page')}
        >
          📄<br/>页面
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'style' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'style' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('style')}
        >
          🎨<br/>风格
        </div>
      </div>

      {/* 展开的面板 */}
      {expandedMenu && (
        <div style={{
          position: 'fixed',
          left: '60px',
          top: '0',
          width: '400px',
          height: '100vh',
          background: 'white',
          boxShadow: '2px 0 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1001,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板头部 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '16px 20px',
            borderBottom: '1px solid #e5e5e5',
            background: '#f8f9fa'
          }}>
            <span style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>
              {expandedMenu === 'module' && '模块管理'}
              {expandedMenu === 'page' && '页面管理'}
              {expandedMenu === 'style' && '风格管理'}
            </span>
            <button
              style={{
                background: 'none',
                border: 'none',
                fontSize: '16px',
                cursor: 'pointer',
                color: '#666',
                padding: '4px',
                borderRadius: '4px'
              }}
              onClick={() => setExpandedMenu(null)}
            >
              ✕
            </button>
          </div>

          {/* 面板内容 */}
          <div style={{ flex: 1, padding: '20px', overflow: 'auto' }}>
            {expandedMenu === 'module' && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <h4 style={{ margin: 0, color: '#333' }}>
                    组件库
                    <span style={{
                      background: '#28a745',
                      color: 'white',
                      fontSize: '10px',
                      padding: '2px 6px',
                      borderRadius: '10px',
                      marginLeft: '8px'
                    }}>
                      LCanvas 模式
                    </span>
                  </h4>
                  <button
                    style={{
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                    onClick={handleResetCanvas}
                    title="清空画布"
                  >
                    🗑️ 重置
                  </button>
                </div>

                {/* 容器组件区域 */}
                <div style={{ marginBottom: '20px' }}>
                  <h5 style={{ margin: '0 0 12px 0', color: '#666', fontSize: '14px' }}>📦 容器组件</h5>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                    {getContainerComponents().map((container) => (
                      <div
                        key={`container-${container.type}`}
                        style={{
                          padding: '12px',
                          border: '1px solid #e5e5e5',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('🖱️ 点击容器组件:', container.type);
                          handleAddComponent(container.type, container.name);
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#17a2b8'
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(23, 162, 184, 0.15)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e5e5e5'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <span style={{ fontSize: '20px' }}>{container.icon}</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '14px' }}>{container.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>{container.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* NUI 组件区域 */}
                <div>
                  <h5 style={{ margin: '0 0 12px 0', color: '#666', fontSize: '14px' }}>🧩 NUI 组件</h5>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                    {/* 显示 NUI 注册的组件 */}
                    {getAvailableComponents().map((component) => (
                      <div
                        key={`nui-${component.type}`}
                        style={{
                          padding: '12px',
                          border: '1px solid #e5e5e5',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('🖱️ 点击 NUI 组件:', component.type);
                          handleAddComponent(component.type, component.name);
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#28a745'
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.15)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e5e5e5'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <span style={{ fontSize: '20px' }}>{component.icon}</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '14px' }}>{component.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>NUI 组件</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {expandedMenu === 'page' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>页面管理</h4>

                {/* 页面树组件 */}
                <div style={{
                  height: 'calc(100vh - 200px)',
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Tree
                    data={pageTreeData}
                    draggable={true}
                    showSearch={true}
                    searchPlaceholder="搜索页面..."
                    searchValue={pageSearchValue}
                    onSearchChange={handlePageSearchChange}
                    defaultExpandAll={true}
                    expandOnClickNode={true}
                    onNodeClick={handlePageNodeClick}
                    renderContent={renderPageNode}
                    indent={16}
                  />
                </div>

                {/* 选中页面信息 */}
                {selectedPage && (
                  <div style={{
                    marginTop: '16px',
                    padding: '12px',
                    background: '#f8f9fa',
                    border: '1px solid #e5e5e5',
                    borderRadius: '6px'
                  }}>
                    <h5 style={{
                      margin: '0 0 8px 0',
                      color: '#333',
                      fontSize: '13px',
                      fontWeight: '600'
                    }}>
                      当前选中页面
                    </h5>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div><strong>名称:</strong> {selectedPage.label}</div>
                      {selectedPage.path && <div><strong>路径:</strong> {selectedPage.path}</div>}
                      {selectedPage.type && <div><strong>类型:</strong> {selectedPage.type}</div>}
                      {selectedPage.status && <div><strong>状态:</strong> {selectedPage.status}</div>}
                    </div>
                    <div style={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                      <button style={{
                        padding: '4px 8px',
                        background: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        编辑页面
                      </button>
                      <button style={{
                        padding: '4px 8px',
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        预览页面
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {expandedMenu === 'style' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>风格设置</h4>
                <p style={{ color: '#666', fontSize: '14px' }}>风格管理功能开发中...</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        marginLeft: '0'
      }}>
        {/* 工具栏 - 移除画布模式切换 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '12px 20px',
          background: 'white',
          borderBottom: '1px solid #e5e5e5',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <span style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              borderRadius: '4px',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              📝 LCanvas 设计器
            </span>
            <span style={{ color: '#666', fontSize: '12px' }}>
              通过左侧模块面板添加和管理组件
            </span>
          </div>
          <button
            style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onClick={() => handleMenuClick('module')}
          >
            添加组件
          </button>
        </div>

        {/* 画布区域 - 统一使用 LCanvas 模式 */}
        <div style={{
          flex: 1,
          padding: '20px',
          background: 'white',
          margin: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          <LCanvasMode 
            schema={schema} 
            onSchemaChange={handleSchemaChange} 
            onAddComponentToContainer={addComponentToContainer}
            pendingComponent={pendingComponent}
            onCancelPending={cancelPendingComponent}
          />
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div style={{
        width: '320px',
        height: '100vh',
        background: 'white',
        borderLeft: '1px solid #e5e5e5',
        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* 面板头部 */}
        <div style={{
          padding: '16px 20px',
          borderBottom: '1px solid #e5e5e5',
          background: '#f8f9fa'
        }}>
          <h4 style={{ margin: '0', color: '#333', fontSize: '16px', fontWeight: '600' }}>
            属性面板
          </h4>
        </div>

        {/* 面板内容 */}
        <div style={{
          flex: 1,
          padding: '20px',
          overflow: 'auto'
        }}>
          {/* Schema 信息 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📋 Schema 结构
              <span style={{
                background: '#007bff',
                color: 'white',
                fontSize: '10px',
                padding: '2px 6px',
                borderRadius: '10px'
              }}>
                {Array.isArray(schema.body) ? schema.body.length : 0} 个组件
              </span>
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              overflow: 'hidden'
            }}>
              <pre style={{
                margin: '0',
                padding: '16px',
                fontSize: '11px',
                lineHeight: '1.5',
                color: '#333',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                overflow: 'auto',
                maxHeight: '400px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                {JSON.stringify(schema, null, 2)}
              </pre>
            </div>
          </div>

          {/* 组件统计 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              📊 组件统计
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              padding: '12px'
            }}>
              {schema.body && Array.isArray(schema.body) && schema.body.length > 0 ? (
                <div>
                  {Object.entries(
                    schema.body.reduce((acc: Record<string, number>, item) => {
                      acc[item.component] = (acc[item.component] || 0) + 1
                      return acc
                    }, {})
                  ).map(([component, count]) => (
                    <div key={component} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '4px 0',
                      fontSize: '12px'
                    }}>
                      <span style={{ color: '#333' }}>
                        {component === 'button' && '🔘 按钮'}
                        {component === 'text' && '📝 文字'}
                        {component === 'div' && '📦 容器'}
                        {component === 'AbsoluteSection' && '🎯 绝对定位'}
                        {component === 'FlowSection' && '🌊 流式布局'}
                        {!['button', 'text', 'div', 'AbsoluteSection', 'FlowSection'].includes(component) && `🔧 ${component}`}
                      </span>
                      <span style={{
                        background: '#007bff',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        minWidth: '20px',
                        textAlign: 'center'
                      }}>
                        {count}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  color: '#999',
                  fontSize: '12px',
                  textAlign: 'center',
                  padding: '20px 0'
                }}>
                  暂无组件
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              🛠️ 操作
            </h5>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleExportSchema}
                onMouseEnter={(e) => e.currentTarget.style.background = '#218838'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#28a745'}
              >
                📥 导出 Schema
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleClearCanvas}
                onMouseEnter={(e) => e.currentTarget.style.background = '#c82333'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#dc3545'}
              >
                🗑️ 清空画布
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleResetPositions}
                onMouseEnter={(e) => e.currentTarget.style.background = '#5a6268'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#6c757d'}
              >
                🔄 重置位置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// LCanvas 模式组件
const LCanvasMode = ({ schema, onSchemaChange, onAddComponentToContainer, pendingComponent, onCancelPending }: {
  schema: BaseSchema,
  onSchemaChange: (schema: BaseSchema) => void,
  onAddComponentToContainer?: (containerId: string, componentType: string, componentName: string) => void,
  pendingComponent: { type: string; name: string } | null,
  onCancelPending: () => void
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [highlightedContainer, setHighlightedContainer] = useState<string | null>(null);

  // 投放模式状态
  const isInDropMode = !!pendingComponent;

  // 使用与 DemoComp 相同的初始化方式
  useEffect(() => {
    const init = async () => {
      try {
        console.log('🚀 开始初始化 LCanvas（使用 DemoComp 方式）...');

        // 直接使用 BladeRender.getInstance()
        const render = BladeRender.getInstance();

        // 检查并加载 WASM
        if (!BladeRender.isWASMReady) {
          console.log('📦 加载 WASM...');
          await render.loadWASM();
          console.log('✅ WASM 加载完成');
        }

        // 定义变量
        console.log('📝 定义变量...');
        await render.defineVariable("isAdmin", "0");

        // 为每种组件类型预设 props 变量
        // 这些变量会被 NUI 组件的 PHP 模板使用
        await render.defineVariable("props", {
          // 通用属性
          dataId: "",
          
          // 按钮组件属性
          text: "按钮111",
          type: "primary",
          size: "medium",
          backgroundColor: "#409eff",
          textColor: "#ffffff",
          borderColor: "#409eff",
          borderRadius: "4px",
          fontSize: "14px",
          padding: "12px 20px",
          disabled: false,
          plain: false,
          round: false,
          icon: false,
          
          // 文本组件属性
          content: "文本内容",
          color: "#333333",
          fontWeight: "normal",
          textAlign: "left",
          lineHeight: "1.5",
          fontFamily: "inherit",
          textDecoration: "none",
          
          // 图标组件属性
          name: "star",
          
          // 图片组件属性
          src: "https://via.placeholder.com/150x100",
          alt: "图片",
          width: "150px",
          height: "100px"
        });
        
        console.log('✅ 变量定义完成');

        setIsInitialized(true);
        console.log('🎉 LCanvas 初始化完成');

      } catch (err) {
        console.error('❌ LCanvas 初始化失败:', err);
        setIsInitialized(true); // 即使失败也设置为已初始化
      }
    };

    if (!isInitialized) {
      init();
    }
  }, [isInitialized]);

  // 分离容器组件和普通组件
  const separateComponents = () => {
    if (!Array.isArray(schema.body)) return { containers: [], others: [] };
    
    const containers = schema.body.filter(item => 
      item.component === 'AbsoluteSection' || item.component === 'FlowSection'
    );
    const others = schema.body.filter(item => 
      item.component !== 'AbsoluteSection' && item.component !== 'FlowSection'
    );
    
    return { containers, others };
  };

  // 处理容器点击
  const handleContainerClick = (containerId: string) => {
    if (pendingComponent && onAddComponentToContainer) {
      // 投放组件到容器中
      onAddComponentToContainer(containerId, pendingComponent.type, pendingComponent.name);
    }
  };

  // 渲染容器组件
  const renderContainerComponent = (containerSchema: BaseSchema) => {
    const isHighlighted = highlightedContainer === containerSchema.id;
    const canDrop = isInDropMode; // 投放模式下可以投放

    const handleContainerSchemaChange = (newSchema: BaseSchema) => {
      // 更新特定容器的 schema
      const updatedBody = Array.isArray(schema.body) ? schema.body.map((item: BaseSchema) => 
        item.id === containerSchema.id ? newSchema : item
      ) : [];
      
      onSchemaChange({
        ...schema,
        body: updatedBody
      });
    };

    const containerStyle: React.CSSProperties = {
      position: 'relative',
      border: isHighlighted ? '2px solid #007bff' : '1px solid transparent',
      borderRadius: '4px',
      transition: 'border-color 0.2s ease',
      cursor: canDrop ? 'copy' : 'default',
      // 去掉多余的背景和间距，实现无缝拼接
      margin: '0',
      padding: '0'
    };

    if (containerSchema.component === 'AbsoluteSection') {
      return (
        <div
          style={{
            ...containerStyle,
            overflow: 'visible'
          }}
          onMouseEnter={() => setHighlightedContainer(containerSchema.id)}
          onMouseLeave={() => setHighlightedContainer(null)}
          onClick={() => handleContainerClick(containerSchema.id)}
        >
          <style>{`
            /* 覆盖 AbsoluteSection 的默认样式 */
            .absolute-section-${containerSchema.id} {
              border: none !important;
              background-color: transparent !important;
              border-radius: 0 !important;
              padding: 8px !important;
              min-height: 120px !important;
              max-width: 100% !important;
              overflow: visible !important;
            }
            
            .absolute-section-${containerSchema.id}:hover {
              border: none !important;
            }
          `}</style>
          <div className={`absolute-section-${containerSchema.id}`}>
            <AbsoluteSection
              key={containerSchema.id}
              schema={containerSchema}
              width="100%"
              height="auto" // 改为自动高度
              onSchemaChange={handleContainerSchemaChange}
            />
          </div>
        </div>
      );
    } else if (containerSchema.component === 'FlowSection') {
      return (
        <div
          style={{
            ...containerStyle,
            // 覆盖 FlowSection 的默认样式
            overflow: 'visible'
          }}
          onMouseEnter={() => setHighlightedContainer(containerSchema.id)}
          onMouseLeave={() => setHighlightedContainer(null)}
          onClick={() => handleContainerClick(containerSchema.id)}
        >
          <style>{`
            /* 覆盖 FlowSection 的默认样式 */
            .flow-section-${containerSchema.id} {
              border: none !important;
              background-color: transparent !important;
              border-radius: 0 !important;
              padding: 8px !important;
              min-height: 60px !important;
              height: auto !important;
              max-width: 100% !important;
              overflow: visible !important;
            }
            
            .flow-section-${containerSchema.id}:hover {
              border: none !important;
            }
          `}</style>
          <div className={`flow-section-${containerSchema.id}`}>
            <FlowSection
              key={containerSchema.id}
              schema={containerSchema}
              width="100%" // 使用百分比而不是固定数值
              height="auto" // 改为自动高度，避免覆盖问题
              direction="row"
              wrap={true}
              gap={8} // 减小间距
              rowGap={6}
              columnGap={8}
              justify="flex-start"
              align="flex-start"
              onSchemaChange={handleContainerSchemaChange}
            />
          </div>
        </div>
      );
    }
    return null;
  };

  // 自定义组件渲染器，为每个组件正确设置 props
  const CustomBladeRenderer = ({ schema: renderSchema }: { schema: BaseSchema }) => {
    const [renderedContent, setRenderedContent] = useState<string>('');
    const [isRendering, setIsRendering] = useState(false);

    useEffect(() => {
      const renderWithProps = async () => {
        if (!renderSchema || !isInitialized) return;
        
        setIsRendering(true);
        try {
          const render = BladeRender.getInstance();
          
          // 如果 schema 有子组件，我们需要为每个组件单独渲染
          if (Array.isArray(renderSchema.body) && renderSchema.body.length > 0) {
            const renderedChildren = await Promise.all(
              renderSchema.body.map(async (child) => {
                if (child.props) {
                  // 为每个子组件设置特定的 props
                  await render.defineVariable("props", child.props);
                }
                
                if (child.template) {
                  return await render.executePhp(child.template);
                }
                return '';
              })
            );
            
            // 将所有子组件的 HTML 组合起来
            const combinedContent = renderedChildren.join('');
            
            // 渲染根容器，将子组件内容插入 slot
            let rootTemplate = renderSchema.template || '<div><slot></slot></div>';
            rootTemplate = rootTemplate.replace(/<slot[\s]*><\/slot>/g, combinedContent);
            
            const finalContent = await render.executePhp(rootTemplate);
            setRenderedContent(finalContent);
          } else {
            // 没有子组件，直接渲染
            const content = await render.render(renderSchema) as any;
            setRenderedContent(content?.props?.dangerouslySetInnerHTML?.__html || '');
          }
        } catch (error) {
          console.error('❌ 自定义渲染失败:', error);
          setRenderedContent(`<div style="color: red;">渲染失败: ${error}</div>`);
        } finally {
          setIsRendering(false);
        }
      };

      renderWithProps();
    }, [renderSchema, isInitialized]);

    if (isRendering) {
      return <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>渲染中...</div>;
    }

    return (
      <div 
        dangerouslySetInnerHTML={{ __html: renderedContent }}
        style={{ minHeight: '20px' }}
      />
    );
  };

  if (!isInitialized) {
    return (
      <div style={{
        border: '2px dashed #ddd',
        borderRadius: '8px',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666',
        fontSize: '16px',
        flexDirection: 'column'
      }}>
        <div>⏳ 正在初始化 LCanvas...</div>
        <small style={{ marginTop: '10px', display: 'block' }}>
          首次加载可能需要几秒钟
        </small>
      </div>
    );
  }

  const { containers, others } = separateComponents();

  return (
    <div style={{ position: 'relative' }}>
      {/* 缓存调试面板 */}
      <CacheDebugPanel />

      {/* 投放模式提示 */}
      {isInDropMode && (
        <div style={{
          position: 'fixed',
          top: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: '#007bff',
          color: 'white',
          padding: '12px 20px',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          zIndex: 10000,
          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <span>🎯 点击目标容器投放：{pendingComponent?.name}</span>
          <button
            style={{
              background: 'rgba(255,255,255,0.2)',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
            onClick={onCancelPending}
          >
            取消
          </button>
        </div>
      )}

      {/* 主渲染区域 */}
      <div style={{
        border: '2px solid #28a745',
        borderRadius: '8px',
        minHeight: '500px',
        overflow: 'auto',
        background: 'white',
        position: 'relative'
      }}>
        {/* 调试信息 */}
        <div style={{
          position: 'absolute',
          top: '5px',
          right: '5px',
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 1000
        }}>
          容器: {containers.length} | 独立组件: {others.length}
        </div>

        <div style={{ padding: '0' }}> {/* 去掉 padding */}
          {/* 如果没有容器，显示默认区域 */}
          {containers.length === 0 && (
            <div style={{
              border: '2px dashed #ddd',
              borderRadius: '8px',
              padding: '40px',
              textAlign: 'center',
              color: '#666',
              margin: '20px'
            }}>
              <h3 style={{ margin: '0 0 10px 0' }}>🏗️ 空白画布</h3>
              <p style={{ margin: '0' }}>请从左侧添加容器组件开始设计</p>
            </div>
          )}

          {/* 渲染容器组件 - 无缝拼接 */}
          {containers.map((containerSchema) => (
            <div key={containerSchema.id} style={{ marginBottom: '0' }}> {/* 去掉间距 */}
              {renderContainerComponent(containerSchema)}
            </div>
          ))}

          {/* 独立组件区域 - 只在有独立组件时显示，避免重复渲染 */}
          {others.length > 0 && (
            <div style={{ margin: '20px', marginTop: '24px' }}>
              <div style={{
                background: '#fff3cd',
                border: '1px solid #ffeaa7',
                borderRadius: '6px',
                padding: '12px',
                marginBottom: '12px'
              }}>
                <h5 style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#856404' }}>
                  📝 独立组件区域
                </h5>
                <p style={{ margin: '0', fontSize: '12px', color: '#856404' }}>
                  这些组件未放置在任何容器中，建议拖拽到上方的容器内
                </p>
              </div>
              
              {/* 为独立组件创建一个干净的 schema，使用自定义渲染器 */}
              <CustomBladeRenderer schema={{
                component: 'div',
                id: 'independent-components',
                template: '<div style="padding: 16px; background: #f8f9fa; border-radius: 6px;"><slot></slot></div>',
                body: others
              }} />
            </div>
          )}
        </div>
      </div>

      {/* 模式说明 */}
      <div style={{
        marginTop: '10px',
        padding: '12px',
        background: '#e7f3ff',
        border: '1px solid #b3d9ff',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#0066cc'
      }}>
        💡 <strong>LCanvas 容器设计器</strong>：
        <br />• 容器组件（绝对定位/流式布局）作为独立的设计区域，无缝拼接
        <br />• 点击左侧普通组件进入投放模式，然后点击目标容器完成投放
        <br />• NUI 组件（按钮、文本、图标）支持自定义样式、文本和属性
        <br />• 鼠标移入容器时会显示边框高亮，支持嵌套布局和组件编辑
      </div>
    </div>
  );
};

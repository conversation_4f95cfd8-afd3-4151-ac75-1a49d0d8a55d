import { useState, useEffect } from 'react'
import { AbsoluteSection, FlowSection, BladeView, useBladeRender, CacheDebugPanel } from 'lcanvas'
import Tree, { type TreeNode } from './component/structTree/Tree'
import type { BaseSchema } from 'nui'

export default () => {
  const [schema, setSchema] = useState<BaseSchema>({
    component: 'div',
    id: '1',
    template: '<div><slot></slot></div>',
    body: [
      {
        component: 'div',
        id: '2222',
        template: '<div>默认内容</div>'
      }
    ]
  })

  const [expandedMenu, setExpandedMenu] = useState<string | null>(null)
  const [canvasMode, setCanvasMode] = useState<'lcanvas' | 'absolute' | 'flow'>('absolute')

  // 页面树数据
  const [pageTreeData] = useState<TreeNode[]>([
    {
      id: 'app-pages',
      label: '应用页面',
      children: [
        {
          id: 'home',
          label: '首页',
          icon: '🏠',
          type: 'page',
          path: '/home',
          status: 'published'
        },
        {
          id: 'about',
          label: '关于我们',
          icon: '📋',
          type: 'page',
          path: '/about',
          status: 'draft'
        },
        {
          id: 'products',
          label: '产品中心',
          icon: '📦',
          type: 'page',
          path: '/products',
          status: 'published',
          children: [
            {
              id: 'product-list',
              label: '产品列表',
              icon: '📝',
              type: 'page',
              path: '/products/list',
              status: 'published'
            },
            {
              id: 'product-detail',
              label: '产品详情',
              icon: '🔍',
              type: 'page',
              path: '/products/detail',
              status: 'published'
            }
          ]
        },
        {
          id: 'contact',
          label: '联系我们',
          icon: '📞',
          type: 'page',
          path: '/contact',
          status: 'published'
        }
      ]
    },
    {
      id: 'system-pages',
      label: '系统页面',
      children: [
        {
          id: 'login',
          label: '登录页',
          icon: '🔐',
          type: 'system',
          path: '/login',
          status: 'published'
        },
        {
          id: 'register',
          label: '注册页',
          icon: '📝',
          type: 'system',
          path: '/register',
          status: 'published'
        },
        {
          id: '404',
          label: '404错误页',
          icon: '❌',
          type: 'system',
          path: '/404',
          status: 'published'
        },
        {
          id: 'admin',
          label: '管理后台',
          icon: '⚙️',
          type: 'system',
          path: '/admin',
          status: 'published',
          children: [
            {
              id: 'admin-dashboard',
              label: '仪表盘',
              icon: '📊',
              type: 'system',
              path: '/admin/dashboard',
              status: 'published'
            },
            {
              id: 'admin-users',
              label: '用户管理',
              icon: '👥',
              type: 'system',
              path: '/admin/users',
              status: 'published'
            },
            {
              id: 'admin-settings',
              label: '系统设置',
              icon: '⚙️',
              type: 'system',
              path: '/admin/settings',
              status: 'draft'
            }
          ]
        }
      ]
    }
  ])

  const [selectedPage, setSelectedPage] = useState<TreeNode | null>(null)
  const [pageSearchValue, setPageSearchValue] = useState('')

  const handleMenuClick = (menuId: string) => {
    if (expandedMenu === menuId) {
      setExpandedMenu(null)
    } else {
      setExpandedMenu(menuId)
    }
  }

  const handleSchemaChange = (newSchema: BaseSchema) => {
    console.log('Schema 更新前:', schema)
    console.log('Schema 更新后:', newSchema)
    setSchema(newSchema)
  }

  const handleAddComponent = (componentType: string, componentName: string) => {
    const newComponentId = `${componentType}-${Date.now()}`

    // 根据不同模式生成不同的组件结构
    let newComponent: BaseSchema;

    if (canvasMode === 'lcanvas') {
      // LCanvas 模式：生成带 Blade 模板的组件
      newComponent = createLCanvasComponent(componentType, newComponentId, componentName);
    } else {
      // 其他模式：生成普通组件
      newComponent = {
        component: componentType,
        id: newComponentId,
        name: componentName,
        template: `<${componentType === 'div' ? 'div' : componentType}>${componentName}</${componentType === 'div' ? 'div' : componentType}>`
      };
    }

    // 更新 schema，添加新组件到 body 数组中
    const currentBody = Array.isArray(schema.body) ? schema.body : []
    const updatedSchema = {
      ...schema,
      body: [...currentBody, newComponent]
    }

    console.log('添加组件:', componentType, componentName, '模式:', canvasMode)
    console.log('更新前 schema:', schema)
    console.log('更新后 schema:', updatedSchema)
    setSchema(updatedSchema)

    // 关闭模块面板
    setExpandedMenu(null)
  }

  // 创建 LCanvas 组件的辅助函数
  const createLCanvasComponent = (componentType: string, id: string, name: string): BaseSchema => {
    switch (componentType) {
      case 'text':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <h4>${name}</h4>
            <p>这是一个文本组件，支持 Blade 语法</p>
            <p>当前时间: {{ date('Y-m-d H:i:s') }}</p>
          </div>`
        };

      case 'button':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px;">
            <button style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
              ${name} - 点击我
            </button>
          </div>`
        };

      case 'card':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 15px; margin: 10px; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background: white;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${name}</h3>
            <p style="margin: 0 0 10px 0; color: #666;">这是一个卡片组件</p>
            @if($data['isAdmin'])
              <div style="color: #28a745; font-size: 12px;">管理员可见内容</div>
            @else
              <div style="color: #6c757d; font-size: 12px;">普通用户内容</div>
            @endif
          </div>`
        };

      case 'list':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <h4>${name}</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
              @foreach([1, 2, 3, 4, 5] as $item)
                <li style="margin: 5px 0;">列表项 {{ $item }}</li>
              @endforeach
            </ul>
          </div>`
        };

      case 'conditional':
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 15px; margin: 10px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">${name} - 条件渲染</h4>
            @if($data['isAdmin'])
              <div style="padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">
                ✅ 管理员权限：您可以看到这个内容
              </div>
            @else
              <div style="padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24;">
                ❌ 普通用户：权限不足
              </div>
            @endif
          </div>`
        };

      default:
        return {
          component: 'div',
          id,
          name,
          template: `<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <p>${name} - ${componentType}</p>
          </div>`
        };
    }
  }

  const handleExportSchema = () => {
    const dataStr = JSON.stringify(schema, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'schema.json'
    link.click()
    URL.revokeObjectURL(url)
  }

  const handleClearCanvas = () => {
    if (confirm('确定要清空画布吗？此操作不可撤销。')) {
      setSchema({
        component: 'div',
        id: '1',
        template: '<div><slot></slot></div>',
        body: []
      })
    }
  }

  const handleResetPositions = () => {
    // 这个功能需要 AbsoluteSection 组件支持，暂时显示提示
    alert('重置位置功能需要在 AbsoluteSection 组件中实现')
  }

  const handlePageNodeClick = (node: TreeNode) => {
    setSelectedPage(node)
    console.log('选中页面:', node)
  }

  const handlePageSearchChange = (value: string) => {
    setPageSearchValue(value)
  }

  const renderPageNode = (node: TreeNode) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'published': return '#28a745'
        case 'draft': return '#ffc107'
        case 'archived': return '#6c757d'
        default: return '#6c757d'
      }
    }

    const getStatusText = (status: string) => {
      switch (status) {
        case 'published': return '已发布'
        case 'draft': return '草稿'
        case 'archived': return '已归档'
        default: return '未知'
      }
    }

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '14px' }}>{node.icon}</span>
          <span style={{ fontSize: '13px', color: '#333' }}>{node.label}</span>
          {node.path && (
            <span style={{
              fontSize: '11px',
              color: '#999',
              fontFamily: 'monospace'
            }}>
              {node.path}
            </span>
          )}
        </div>
        {node.status && (
          <span style={{
            fontSize: '10px',
            padding: '2px 6px',
            borderRadius: '10px',
            backgroundColor: getStatusColor(node.status),
            color: 'white'
          }}>
            {getStatusText(node.status)}
          </span>
        )}
      </div>
    )
  }

  return (
    <div style={{ 
      display: 'flex', 
      height: '100vh', 
      background: '#f5f5f5' 
    }}>
      {/* 左侧菜单 */}
      <div style={{
        width: '60px',
        background: 'linear-gradient(180deg, #ff6b6b 0%, #ee5a52 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        padding: '10px 0',
        position: 'relative',
        zIndex: 1000
      }}>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'module' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'module' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('module')}
        >
          🧩<br/>模块
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'page' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'page' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('page')}
        >
          📄<br/>页面
        </div>
        <div
          style={{
            color: 'white',
            fontSize: '12px',
            textAlign: 'center',
            marginBottom: '20px',
            padding: '10px 5px',
            cursor: 'pointer',
            borderRadius: '4px',
            background: expandedMenu === 'style' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
            borderLeft: expandedMenu === 'style' ? '3px solid #fff' : '3px solid transparent'
          }}
          onClick={() => handleMenuClick('style')}
        >
          🎨<br/>风格
        </div>
      </div>

      {/* 展开的面板 */}
      {expandedMenu && (
        <div style={{
          position: 'fixed',
          left: '60px',
          top: '0',
          width: '400px',
          height: '100vh',
          background: 'white',
          boxShadow: '2px 0 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1001,
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 面板头部 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '16px 20px',
            borderBottom: '1px solid #e5e5e5',
            background: '#f8f9fa'
          }}>
            <span style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>
              {expandedMenu === 'module' && '模块管理'}
              {expandedMenu === 'page' && '页面管理'}
              {expandedMenu === 'style' && '风格管理'}
            </span>
            <button
              style={{
                background: 'none',
                border: 'none',
                fontSize: '16px',
                cursor: 'pointer',
                color: '#666',
                padding: '4px',
                borderRadius: '4px'
              }}
              onClick={() => setExpandedMenu(null)}
            >
              ✕
            </button>
          </div>

          {/* 面板内容 */}
          <div style={{ flex: 1, padding: '20px', overflow: 'auto' }}>
            {expandedMenu === 'module' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>
                  组件库
                  {canvasMode === 'lcanvas' && (
                    <span style={{
                      background: '#28a745',
                      color: 'white',
                      fontSize: '10px',
                      padding: '2px 6px',
                      borderRadius: '10px',
                      marginLeft: '8px'
                    }}>
                      LCanvas 模式
                    </span>
                  )}
                </h4>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>

                  {/* LCanvas 模式专用组件 */}
                  {canvasMode === 'lcanvas' && (
                    <>
                      <div
                        style={{
                          padding: '12px',
                          border: '1px solid #e5e5e5',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={() => handleAddComponent('card', '卡片')}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#28a745'
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.15)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e5e5e5'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <span style={{ fontSize: '20px' }}>🃏</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '14px' }}>卡片</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>支持条件渲染</div>
                        </div>
                      </div>

                      <div
                        style={{
                          padding: '12px',
                          border: '1px solid #e5e5e5',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={() => handleAddComponent('list', '列表')}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#28a745'
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.15)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e5e5e5'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <span style={{ fontSize: '20px' }}>📋</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '14px' }}>列表</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>循环渲染</div>
                        </div>
                      </div>

                      <div
                        style={{
                          padding: '12px',
                          border: '1px solid #e5e5e5',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          transition: 'all 0.2s ease'
                        }}
                        onClick={() => handleAddComponent('conditional', '条件组件')}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = '#28a745'
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.15)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = '#e5e5e5'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <span style={{ fontSize: '20px' }}>🔀</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '14px' }}>条件组件</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>权限控制</div>
                        </div>
                      </div>
                    </>
                  )}

                  {/* 通用组件 */}
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('text', '文字')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>📝</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>文字</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>基础文本组件</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('button', '按钮')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🔘</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>按钮</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>可点击按钮</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('div', '容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>📦</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>容器</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>通用容器</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('AbsoluteSection', '绝对定位容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🎯</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>绝对定位</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>拖拽容器</div>
                    </div>
                  </div>
                  <div
                    style={{
                      padding: '12px',
                      border: '1px solid #e5e5e5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleAddComponent('FlowSection', '流式布局容器')}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#007bff'
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 123, 255, 0.15)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#e5e5e5'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    <span style={{ fontSize: '20px' }}>🌊</span>
                    <div>
                      <div style={{ fontWeight: '500', fontSize: '14px' }}>流式布局</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>自动排列容器</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {expandedMenu === 'page' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>页面管理</h4>

                {/* 页面树组件 */}
                <div style={{
                  height: 'calc(100vh - 200px)',
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Tree
                    data={pageTreeData}
                    draggable={true}
                    showSearch={true}
                    searchPlaceholder="搜索页面..."
                    searchValue={pageSearchValue}
                    onSearchChange={handlePageSearchChange}
                    defaultExpandAll={true}
                    expandOnClickNode={true}
                    onNodeClick={handlePageNodeClick}
                    renderContent={renderPageNode}
                    indent={16}
                  />
                </div>

                {/* 选中页面信息 */}
                {selectedPage && (
                  <div style={{
                    marginTop: '16px',
                    padding: '12px',
                    background: '#f8f9fa',
                    border: '1px solid #e5e5e5',
                    borderRadius: '6px'
                  }}>
                    <h5 style={{
                      margin: '0 0 8px 0',
                      color: '#333',
                      fontSize: '13px',
                      fontWeight: '600'
                    }}>
                      当前选中页面
                    </h5>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div><strong>名称:</strong> {selectedPage.label}</div>
                      {selectedPage.path && <div><strong>路径:</strong> {selectedPage.path}</div>}
                      {selectedPage.type && <div><strong>类型:</strong> {selectedPage.type}</div>}
                      {selectedPage.status && <div><strong>状态:</strong> {selectedPage.status}</div>}
                    </div>
                    <div style={{ marginTop: '8px', display: 'flex', gap: '8px' }}>
                      <button style={{
                        padding: '4px 8px',
                        background: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        编辑页面
                      </button>
                      <button style={{
                        padding: '4px 8px',
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        cursor: 'pointer',
                        fontSize: '11px'
                      }}>
                        预览页面
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {expandedMenu === 'style' && (
              <div>
                <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>风格设置</h4>
                <p style={{ color: '#666', fontSize: '14px' }}>风格管理功能开发中...</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        marginLeft: '0'
      }}>
        {/* 工具栏 */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '12px 20px',
          background: 'white',
          borderBottom: '1px solid #e5e5e5',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'lcanvas' ? '#007bff' : 'white',
                color: canvasMode === 'lcanvas' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('lcanvas')}
            >
              LCanvas 模式
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'absolute' ? '#007bff' : 'white',
                color: canvasMode === 'absolute' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('absolute')}
            >
              绝对定位模式
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #ddd',
                background: canvasMode === 'flow' ? '#007bff' : 'white',
                color: canvasMode === 'flow' ? 'white' : '#666',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
              onClick={() => setCanvasMode('flow')}
            >
              流式布局模式
            </button>
          </div>
          <button
            style={{
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
            onClick={() => handleMenuClick('module')}
          >
            添加组件
          </button>
        </div>

        {/* 画布区域 */}
        <div style={{
          flex: 1,
          padding: '20px',
          background: 'white',
          margin: '20px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden'
        }}>
          {canvasMode === 'absolute' ? (
            <AbsoluteSection
              schema={schema}
              width="100%"
              height="500px"
              onSchemaChange={handleSchemaChange}
            />
          ) : canvasMode === 'flow' ? (
            <div>
              <div style={{
                padding: '8px 16px',
                backgroundColor: '#e7f3ff',
                border: '1px solid #b3d9ff',
                borderRadius: '4px',
                marginBottom: '12px',
                fontSize: '12px',
                color: '#0066cc'
              }}>
                💡 <strong>流式布局模式</strong>：双击添加组件，拖拽到组件左右侧可插入到指定位置
              </div>
              <FlowSection
                schema={schema}
                width="100%"
                height="500px"
                direction="row"
                wrap={true}
                gap={12}
                rowGap={8}
                columnGap={12}
                justify="flex-start"
                align="flex-start"
                onSchemaChange={handleSchemaChange}
              />
            </div>
          ) : (
            <LCanvasMode schema={schema} onSchemaChange={handleSchemaChange} />
          )}
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div style={{
        width: '320px',
        height: '100vh',
        background: 'white',
        borderLeft: '1px solid #e5e5e5',
        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* 面板头部 */}
        <div style={{
          padding: '16px 20px',
          borderBottom: '1px solid #e5e5e5',
          background: '#f8f9fa'
        }}>
          <h4 style={{ margin: '0', color: '#333', fontSize: '16px', fontWeight: '600' }}>
            属性面板
          </h4>
        </div>

        {/* 面板内容 */}
        <div style={{
          flex: 1,
          padding: '20px',
          overflow: 'auto'
        }}>
          {/* Schema 信息 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              📋 Schema 结构
              <span style={{
                background: '#007bff',
                color: 'white',
                fontSize: '10px',
                padding: '2px 6px',
                borderRadius: '10px'
              }}>
                {Array.isArray(schema.body) ? schema.body.length : 0} 个组件
              </span>
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              overflow: 'hidden'
            }}>
              <pre style={{
                margin: '0',
                padding: '16px',
                fontSize: '11px',
                lineHeight: '1.5',
                color: '#333',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                overflow: 'auto',
                maxHeight: '400px',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                {JSON.stringify(schema, null, 2)}
              </pre>
            </div>
          </div>

          {/* 组件统计 */}
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              📊 组件统计
            </h5>
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #e5e5e5',
              borderRadius: '6px',
              padding: '12px'
            }}>
              {schema.body && Array.isArray(schema.body) && schema.body.length > 0 ? (
                <div>
                  {Object.entries(
                    schema.body.reduce((acc: Record<string, number>, item) => {
                      acc[item.component] = (acc[item.component] || 0) + 1
                      return acc
                    }, {})
                  ).map(([component, count]) => (
                    <div key={component} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '4px 0',
                      fontSize: '12px'
                    }}>
                      <span style={{ color: '#333' }}>
                        {component === 'button' && '🔘 按钮'}
                        {component === 'text' && '📝 文字'}
                        {component === 'div' && '📦 容器'}
                        {component === 'AbsoluteSection' && '🎯 绝对定位'}
                        {component === 'FlowSection' && '🌊 流式布局'}
                        {!['button', 'text', 'div', 'AbsoluteSection', 'FlowSection'].includes(component) && `🔧 ${component}`}
                      </span>
                      <span style={{
                        background: '#007bff',
                        color: 'white',
                        fontSize: '10px',
                        padding: '2px 6px',
                        borderRadius: '10px',
                        minWidth: '20px',
                        textAlign: 'center'
                      }}>
                        {count}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  color: '#999',
                  fontSize: '12px',
                  textAlign: 'center',
                  padding: '20px 0'
                }}>
                  暂无组件
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div>
            <h5 style={{
              margin: '0 0 12px 0',
              color: '#666',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              🛠️ 操作
            </h5>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleExportSchema}
                onMouseEnter={(e) => e.currentTarget.style.background = '#218838'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#28a745'}
              >
                📥 导出 Schema
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleClearCanvas}
                onMouseEnter={(e) => e.currentTarget.style.background = '#c82333'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#dc3545'}
              >
                🗑️ 清空画布
              </button>
              <button
                style={{
                  padding: '8px 12px',
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'background 0.2s ease'
                }}
                onClick={handleResetPositions}
                onMouseEnter={(e) => e.currentTarget.style.background = '#5a6268'}
                onMouseLeave={(e) => e.currentTarget.style.background = '#6c757d'}
              >
                🔄 重置位置
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// LCanvas 模式组件
const LCanvasMode = ({ schema, onSchemaChange }: {
  schema: BaseSchema,
  onSchemaChange: (schema: BaseSchema) => void
}) => {
  const { isReady, isLoading, error, ensureReady, render } = useBladeRender();

  // 初始化 WASM
  useEffect(() => {
    const init = async () => {
      try {
        await ensureReady();
        if (render) {
          // 定义一些基础变量
          await render.defineVariable("isAdmin", "0");
          await render.defineVariable("data", {
            title: "LCanvas 模式",
            content: "这是通过 LCanvas 渲染的内容"
          });
        }
      } catch (err) {
        console.error('LCanvas 初始化失败:', err);
      }
    };
    init();
  }, [ensureReady, render]);

  if (isLoading) {
    return (
      <div style={{
        border: '2px dashed #ddd',
        borderRadius: '8px',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#666',
        fontSize: '16px',
        flexDirection: 'column'
      }}>
        <div>⏳ 正在加载 LCanvas...</div>
        <small style={{ marginTop: '10px', display: 'block' }}>
          首次加载可能需要几秒钟
        </small>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        border: '2px solid #dc3545',
        borderRadius: '8px',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#dc3545',
        fontSize: '16px',
        flexDirection: 'column',
        background: '#f8d7da'
      }}>
        <div>❌ LCanvas 加载失败</div>
        <small style={{ marginTop: '10px', display: 'block' }}>
          {error.message}
        </small>
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '10px',
            padding: '8px 16px',
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重新加载
        </button>
      </div>
    );
  }

  if (!isReady) {
    return (
      <div style={{
        border: '2px dashed #ddd',
        borderRadius: '8px',
        height: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999',
        fontSize: '16px',
        flexDirection: 'column'
      }}>
        <div>🔄 准备 LCanvas 环境...</div>
      </div>
    );
  }

  return (
    <div style={{ position: 'relative' }}>
      {/* 缓存调试面板 */}
      <CacheDebugPanel />

      {/* LCanvas 状态指示 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        background: '#d4edda',
        border: '1px solid #c3e6cb',
        borderRadius: '4px',
        padding: '8px 12px',
        fontSize: '12px',
        color: '#155724',
        zIndex: 10
      }}>
        ✅ LCanvas 模式已激活
      </div>

      {/* BladeView 渲染 */}
      <div style={{
        border: '2px solid #28a745',
        borderRadius: '8px',
        height: '500px',
        overflow: 'auto',
        background: 'white'
      }}>
        <BladeView schema={schema} />
      </div>

      {/* 模式说明 */}
      <div style={{
        marginTop: '10px',
        padding: '12px',
        background: '#e7f3ff',
        border: '1px solid #b3d9ff',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#0066cc'
      }}>
        💡 <strong>LCanvas 模式</strong>：通过左侧模块面板添加组件，schema 会自动更新并通过 BladeView 渲染。
        支持 Blade 模板语法和 PHP 变量。
      </div>
    </div>
  );
};

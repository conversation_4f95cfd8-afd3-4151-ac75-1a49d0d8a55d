import React, { useState } from 'react'
import './ModuleSelector.scss'

export interface ModuleItem {
  id: string
  name: string
  component: string
  icon: string
  category: string
  description?: string
  props?: any
}

const moduleCategories = [
  {
    id: 'basic',
    name: '基础组件',
    modules: [
      {
        id: 'text',
        name: '文字',
        component: 'text',
        icon: '📝',
        category: 'basic',
        description: '基础文本组件'
      },
      {
        id: 'button',
        name: '按钮',
        component: 'button',
        icon: '🔘',
        category: 'basic',
        description: '可点击的按钮组件'
      },
      {
        id: 'input',
        name: '输入框',
        component: 'input',
        icon: '📝',
        category: 'basic',
        description: '文本输入组件'
      },
      {
        id: 'image',
        name: '图片',
        component: 'img',
        icon: '🖼️',
        category: 'basic',
        description: '图片显示组件'
      }
    ]
  },
  {
    id: 'layout',
    name: '布局组件',
    modules: [
      {
        id: 'div',
        name: '容器',
        component: 'div',
        icon: '📦',
        category: 'layout',
        description: '通用容器组件'
      },
      {
        id: 'absolute-section',
        name: '绝对定位容器',
        component: 'AbsoluteSection',
        icon: '🎯',
        category: 'layout',
        description: '支持拖拽的绝对定位容器'
      },
      {
        id: 'flow-section',
        name: '流式布局容器',
        component: 'FlowSection',
        icon: '🌊',
        category: 'layout',
        description: '支持拖拽排序的流式布局容器'
      },
      {
        id: 'grid',
        name: '网格布局',
        component: 'grid',
        icon: '⚏',
        category: 'layout',
        description: '网格布局容器'
      },
      {
        id: 'flex',
        name: '弹性布局',
        component: 'flex',
        icon: '↔️',
        category: 'layout',
        description: '弹性布局容器'
      }
    ]
  },
  {
    id: 'form',
    name: '表单组件',
    modules: [
      {
        id: 'form',
        name: '表单',
        component: 'form',
        icon: '📋',
        category: 'form',
        description: '表单容器'
      },
      {
        id: 'select',
        name: '下拉选择',
        component: 'select',
        icon: '📋',
        category: 'form',
        description: '下拉选择组件'
      },
      {
        id: 'checkbox',
        name: '复选框',
        component: 'checkbox',
        icon: '☑️',
        category: 'form',
        description: '复选框组件'
      },
      {
        id: 'radio',
        name: '单选框',
        component: 'radio',
        icon: '🔘',
        category: 'form',
        description: '单选框组件'
      }
    ]
  }
]

interface ModuleSelectorProps {
  isVisible: boolean
  onModuleSelect?: (module: ModuleItem) => void
  onClose?: () => void
}

export const ModuleSelector: React.FC<ModuleSelectorProps> = ({
  isVisible,
  onModuleSelect,
  onClose
}) => {
  const [activeCategory, setActiveCategory] = useState('basic')
  const [searchTerm, setSearchTerm] = useState('')

  if (!isVisible) return null

  const filteredModules = moduleCategories
    .find(cat => cat.id === activeCategory)
    ?.modules.filter(module =>
      module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      module.description?.toLowerCase().includes(searchTerm.toLowerCase())
    ) || []

  const handleModuleClick = (module: ModuleItem) => {
    onModuleSelect?.(module)
  }

  const handleDragStart = (e: React.DragEvent, module: ModuleItem) => {
    e.dataTransfer.setData('application/json', JSON.stringify(module))
    e.dataTransfer.effectAllowed = 'copy'
  }

  return (
    <div className="module-selector-overlay">
      <div className="module-selector">
        <div className="module-header">
          <h3>组件库</h3>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="module-search">
          <input
            type="text"
            placeholder="搜索组件..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="module-categories">
          {moduleCategories.map(category => (
            <button
              key={category.id}
              className={`category-btn ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>

        <div className="module-list">
          {filteredModules.map(module => (
            <div
              key={module.id}
              className="module-item"
              draggable
              onDragStart={(e) => handleDragStart(e, module)}
              onClick={() => handleModuleClick(module)}
            >
              <div className="module-icon">{module.icon}</div>
              <div className="module-info">
                <div className="module-name">{module.name}</div>
                <div className="module-description">{module.description}</div>
              </div>
            </div>
          ))}
        </div>

        {filteredModules.length === 0 && (
          <div className="no-modules">
            <p>没有找到匹配的组件</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ModuleSelector

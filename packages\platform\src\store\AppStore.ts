import { makeObservable, observable, action } from 'mobx';

export type AppInfo = {
  title:string
}
// 定义 AppStore 类
class AppStore {
  // 定义可观察的状态
  @observable app: boolean = false;
  @observable appInfo:AppInfo = {'title':'未命名应用'}

  constructor() {
    // 使当前类的属性和方法具有 MobX 响应式特性
    makeObservable(this);
  }

  /**
   * 设置加载状态
   * @param loading 加载状态
   */
  @action updateAppInfo( info:Partial<AppInfo> ) {
    this.appInfo = {...this.appInfo,...info};
  }
  
}

// 创建 AppStore 实例
const appStore = new AppStore();

export default appStore;

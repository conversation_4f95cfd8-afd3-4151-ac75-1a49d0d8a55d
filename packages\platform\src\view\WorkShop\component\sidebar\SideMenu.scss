.side-menu {
  width: 60px;
  height: 100vh;
  background: linear-gradient(180deg, #ff6b6b 0%, #ee5a52 100%);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  .menu-list {
    padding: 10px 0;
  }

  .menu-item-container {
    position: relative;
  }

  .menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.active {
      background: rgba(255, 255, 255, 0.2);
      border-left-color: #fff;
    }

    &.expanded {
      background: rgba(255, 255, 255, 0.15);
    }

    .menu-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }

    .menu-name {
      font-size: 12px;
      color: white;
      text-align: center;
      line-height: 1.2;
      font-weight: 500;
    }

    .expand-arrow {
      position: absolute;
      right: 4px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 8px;
      color: white;
      transition: transform 0.3s ease;

      &.rotated {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }

  // 子菜单浮层
  .submenu-overlay {
    position: fixed;
    left: 60px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    display: flex;
    flex-direction: column;

    .submenu-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e5e5e5;
      background: #f8f9fa;

      .submenu-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #e9ecef;
        }
      }
    }

    .submenu-tabs {
      display: flex;
      border-bottom: 1px solid #e5e5e5;

      .tab {
        padding: 12px 20px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        color: #666;
        font-size: 14px;

        &.active {
          color: #007bff;
          border-bottom-color: #007bff;
          background: #f8f9ff;
        }

        &:hover:not(.active) {
          background: #f8f9fa;
        }
      }
    }

    .submenu-search {
      padding: 16px 20px;
      border-bottom: 1px solid #e5e5e5;

      .search-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        outline: none;

        &:focus {
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        &::placeholder {
          color: #999;
        }
      }
    }

    .submenu-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;

      .submenu-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }

        &.active {
          background: #e3f2fd;
          border-left: 3px solid #007bff;
        }

        .submenu-icon {
          font-size: 16px;
          margin-right: 12px;
          width: 20px;
          text-align: center;
        }

        .submenu-content {
          flex: 1;

          .submenu-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .submenu-english {
            font-size: 12px;
            color: #666;
          }
        }

        .submenu-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.2s ease;

          .action-btn {
            background: none;
            border: none;
            padding: 4px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;

            &:hover {
              background: #e9ecef;
            }
          }
        }

        &:hover .submenu-actions {
          opacity: 1;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .side-menu {
    width: 50px;

    .menu-item {
      padding: 10px 6px;

      .menu-name {
        font-size: 10px;
      }
    }

    .submenu-overlay {
      width: 300px;
      left: 50px;
    }
  }
}

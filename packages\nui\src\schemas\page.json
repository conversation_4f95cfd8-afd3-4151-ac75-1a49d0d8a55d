{
  "name": "页面",
  "description": "这是一个页面组件，可以包含其他组件",
  // 平台标识，决定渲染样式等
  "platform":"desktop",
  "version":"0.0.1",
  "i18n":{
    "en":[{"common.name":"name"}],
    "zh-CN":[{"common.name":"name"}]
  },
  "model":{
    "valueA":{
      "name":"abc",
      "value":"111"
    },
    "valueB":{
      "name":"serviceA",
      "type":"service",
      "path":"/api",
      "params":{
        "name":"name",
        "defaultValue":""
      }
    }
  },
  "components": {
    "Page":{
      "component":"page",
      "type": "container",
      "props":{
        "title":{
          "name":"title",
          "value":"这是一个页面",
          "variable":"$title"
        }
      },
      // 渲染组件需要的逻辑
      "umd":"./page.js"
    },
    "Container":{},
    "Loop":{
      // 逻辑组件

    },
    "Button": {
      "version":"0.0.1",
      "name":"按纽",
      "type": "div",
      // 默认样式
      "style": {
        "default":{
          "width:100px"
        },
        "desktop"{
          "width:100px"
        },
        "mobile":{

        }
      },
      "props":{
        "label":{
          "name":"label",
          "value":"这是一个标签",//优先低于variable
          "variable":""//变量，可以是表达式，如：$data, $scope
        },

        "label2":{
          "name":"label",
          // 获取页面数据
          "value":"$valueA"
        },
        "label3":{
          "name":"label",
          // 获取作用域数据（如果scope是循环体域，需要在schema中找到父循环体组件 component= Loop 或者 type=='container/loop'
          "value":"$scope.value.name"  //$scope.value.index或 $index
        },
        "label4":{
          "name":"label",
          // 获取页面数据
          "value":"$valueB"
        }
      }
    }
  },
  "schema":{
    "$ref":"Page",
    "props":{
      "title":{
        "value":"title",
        "variable":"$title"
      }
    },
    "style":{
      "default":[
        "width:200px"
      ]
    },
    "template":'<div><span></span><slot id="Container"></slot><slot id="Loop"></slot></div>',
    "body":[
      {
        "$ref":"Container",
        "props":{
          "label":{
            "value":"label"
          }
        },
        "style":{
          // 个性化样式
          "default":[
            "width:200px"
          ]
        },
        "i18n":{
          // 自定义的i18n数据
        },
        // 用户自定义button
        "template":"<div><button id='{{Button}}'></button></div>",
        "body":[
          {
            "$ref":"Button",
            "props":{
              "label":{
                "value":"label"
              }
            },  
          }
        ]

      },
      {
        "$ref":"Loop",
        "props":{
          "scope":{
            "value":"[1,2,3]",
            "variable":'$this["arr"]'
          }
        },
        "template":"<div><slot id="Button"></slot></div>",
        "body":[{
          "$ref":"Button",
        }]
      }
    ]
  }
  

}
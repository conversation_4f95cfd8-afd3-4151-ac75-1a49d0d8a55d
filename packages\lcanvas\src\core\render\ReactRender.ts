import type { BaseSchema } from "nui";
import { createComponentId, Array2Object } from "../utils";
import type { IRender } from "./IRender";
import React from 'react'

export default class ReactRender implements IRender{
    
    constructor(){

    }
   
    createElement = (element: BaseSchema) => {
      if( !element.id ){
        element.id = createComponentId();
      }
      const props = { ...Array2Object(element.props || []), 'data-id': element.id }
      // debugger

      let children:any[] = []
      if(element.body && Array.isArray(element.body)){
        children = element.body.map(item=>{
          return this.createElement(item)
        })
      }
      const comp = element.component;
      const ele = React.createElement(comp, props,
        typeof element.body == 'string' ? element.body : children
      );
      return ele;
    }

    async render(schema: BaseSchema ) {
      const ele = this.createElement(schema)
      return ele;
    }

}
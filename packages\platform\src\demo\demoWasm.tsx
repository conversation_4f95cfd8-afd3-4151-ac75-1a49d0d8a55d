import React, { useState, useEffect } from 'react';

// 定义组件类型
interface Props {
  [key: string]: any;
}

const DemoWasm: React.FC<Props> = (props) => {
  // 使用 useState 定义状态
  const [ready, setReady] = useState(false);
  const [phpWeb, setPhpWeb] = useState<any>(null);
  const [info, setInfo] = useState({
    input: '',
    output: ''
  });

  const loadPhpWasm = async () => {
    const host = `${window.location.protocol}//${window.location.host}`;
    console.log('host', host);
    const phpModule = await import(`${host}/lib/blade/PhpWeb.mjs`);
    const php = new phpModule.PhpWeb();
    setPhpWeb(php);
    const phpBinary = await php.binary;
    const zipResponse = await fetch(`${host}/lib/blade/blade-template.zip`);
    if (!zipResponse.ok) {
      throw new Error('无法下载 ZIP 文件: ' + zipResponse.status);
    }
    const zipData = await zipResponse.arrayBuffer();
    phpBinary.FS.writeFile('./blade-template.zip', new Uint8Array(zipData));
    if (!phpBinary.FS.analyzePath('./workspace').exists) {
      phpBinary.FS.mkdir('./workspace');
    }
    await php.run(`<?php
                     
                    $zip = new ZipArchive;
                    echo "zip....";

                    $reflection = new ReflectionClass('ZipArchive');
                    echo "类名：" . $reflection->getName() . "\\n";

                    $result = $zip->open("./blade-template.zip");
                    if ($result === TRUE) {
                        if (!$zip->extractTo("./workspace/")) {
                            exit(1);
                        }
                        $zip->close();
                    } else {
                        exit(1);
                    }
                    echo "after extract....";
                ?>`);
    await php.run(`<?php include_once './workspace/blade-template/index.php'; ?>`);
    console.log("ready.");
    setReady(true);
  };

  useEffect(() => {
    const load = async () => {
      await loadPhpWasm();
      let code = `
    <?php
    
    
    $bladeString = '@if($isAdmin) <p>管理员</p> @else <p>普通用户</p> @endif';
$stringOutput = renderBladeString($bladeString, ['isAdmin' => false]);
echo $stringOutput;
    ?>`;
      setInfo(prev => ({ ...prev, input: code }));
      await renderTemplate(code);
    };
    load();
  }, []);

  const onOutput = (event: any) => {
    console.log('输出:', event.detail.join(''));
    setInfo(prev => ({ ...prev, output: event.detail.join('') }));
    if (phpWeb) {
      phpWeb.removeEventListener('output', onOutput);
    }
  };

  const onTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInfo(prev => ({ ...prev, input: event.target.value }));
  };

  const onCompile = async () => {
    console.log('compile...');
    await renderTemplate(info.input);
  };

  const renderTemplate = async (code: string) => {
    if (ready && phpWeb) {
      phpWeb.addEventListener('output', onOutput);
      const exitCode = await phpWeb.run(code);
    }
  };

  return (
    <>
      <div>
        <textarea
          style={{ width: '100%', height: '300px' }}
          value={info.input}
          onChange={onTextChange}
        ></textarea>
        <div>
          <button onClick={onCompile} disabled={!ready}>
            编译
          </button>
        </div>
        <div>
          <p>输出</p>
          <div style={{ border: '1px solid #ccc' }}>{info.output}</div>
        </div>
      </div>
    </>
  );
};

export default DemoWasm;

export * from './types'
export { AbstractSchema } from './AbstractSchema'

// 导入所有组件
import type { BaseSchema } from "./types";
import {Image} from './Image'
import {Text} from './Text'
import {Icon} from './Icon'
import {Button} from './Button'
import {Container} from './Container'

export const definedComponents: Record<string, BaseSchema> = {}

// 自动注册函数
const registerComponent = (ComponentClass: any, componentName: string) => {
  const instance = new ComponentClass([]);
  definedComponents[componentName.toLowerCase()] = {
    component: instance.component,
    name: instance.name,
    id: '',
    template: instance.getTemplatePHP(),
    props: instance.props
  }
}

// 批量注册
const componentsToRegister = [
  { class: Image, name: 'image' },
  { class: Text, name: 'text' },
  { class: Icon, name: 'icon' },
  { class: Button, name: 'button' },
  { class: Container, name: 'container' },
  // 新增组件只需在这里添加一行
]

componentsToRegister.forEach(({ class: ComponentClass, name }) => {
  registerComponent(ComponentClass, name)
})

// // 保留手动注册的基础组件
// definedComponents['div'] = {
//   component: 'div',
//   name: '容器',
//   id: '',
// }
export const getDefinedComponent = (compName:string)=>{
  return definedComponents[compName]
}

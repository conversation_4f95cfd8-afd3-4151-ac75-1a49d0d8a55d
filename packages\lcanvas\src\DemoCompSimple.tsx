import { useState, useEffect } from 'react'
import './core/env.ts'
import './App.scss'
import { BladeView } from './view/render/BladeView.tsx';
import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';

const examleSchema: BaseSchema = {
  component: 'div',
  id: '1',
  template: `<div style="padding: 20px; border: 2px solid #4CAF50; border-radius: 8px;">
    <h3>🎉 简化版测试成功！</h3>
    <p>如果你看到这个消息，说明 WASM 已经成功加载并渲染。</p>
    <p>这是使用传统方式的简化版本，用于测试基本功能。</p>
  </div>`
}

function DemoCompSimple() {
  const [scheme, setSchema] = useState({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        setLoading(true)
        setError(null)
        
        console.log('开始初始化 WASM...')
        
        let render = BladeRender.getInstance();
        
        // 强制重新加载以避免缓存问题
        BladeRender.isWASMReady = false;
        render.php = null;
        
        if (!BladeRender.isWASMReady) {
          console.log('加载 WASM...')
          await render.loadWASM();
        }
        
        // 验证 PHP 实例
        if (!render.php) {
          throw new Error('PHP 实例未正确创建');
        }
        
        console.log('WASM 加载完成，定义变量...')
        await render.defineVariable("isAdmin", "0")
        
        console.log('设置 schema...')
        setSchema(examleSchema);
        
        setLoading(false)
        console.log('初始化完成！')
        
      } catch (err) {
        console.error('DemoCompSimple 初始化失败:', err);
        setError(err instanceof Error ? err.message : '未知错误');
        setLoading(false)
      }
    };

    initializeComponent();
  }, [])

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <Loading />
        <p>正在加载 WASM...</p>
        <small>首次加载可能需要几秒钟</small>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        padding: '20px', 
        background: '#f8d7da', 
        border: '1px solid #f5c6cb', 
        borderRadius: '8px', 
        color: '#721c24' 
      }}>
        <h3>❌ 加载失败</h3>
        <p>错误信息: {error}</p>
        <button 
          onClick={() => window.location.reload()}
          style={{
            padding: '8px 16px',
            border: '1px solid #721c24',
            borderRadius: '4px',
            background: 'transparent',
            color: '#721c24',
            cursor: 'pointer'
          }}
        >
          重新加载
        </button>
      </div>
    );
  }

  return (
    <>
      <div style={{ 
        padding: '10px', 
        background: '#d4edda', 
        border: '1px solid #c3e6cb',
        borderRadius: '4px',
        marginBottom: '10px',
        color: '#155724'
      }}>
        <small>
          ✅ WASM 已就绪 - 简化版测试
        </small>
      </div>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoCompSimple

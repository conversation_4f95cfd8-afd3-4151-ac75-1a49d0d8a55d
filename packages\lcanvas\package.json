{"name": "lcanvas", "private": true, "version": "0.0.0", "type": "module", "main": "./src/index.ts", "scripts": {"start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"nui": "workspace:^", "axios": "^1.11.0", "html-react-parser": "^5.2.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1", "scss": "^0.2.4"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass": "^1.89.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}
import React, { useState } from 'react'
import { LcCanvas } from './component/canvas/LcCanvas'
import { AbsoluteSection } from '../../ndesignUI/container/AbsoluteSection'
import type { BaseSchema } from '../../schemas'

export default (props:any)=>{
  const [schema, setSchema] = useState<BaseSchema>({
    component:'AbsoluteSection',
    dataId:'abs-1',
    body:{
      'btn1':{
        component:'button',
        dataId:'btn1',
        name: '按钮1'
      },
      'div1':{
        component:'div',
        dataId:'div1',
        name: '容器元素'
      },
      'abs-nested':{
        component:'AbsoluteSection',
        dataId:'abs-nested',
        name: '嵌套容器',
        body: {
          'nested-btn':{
            component:'button',
            dataId:'nested-btn',
            name: '嵌套按钮'
          }
        }
      }
    }
  })

  const handleSchemaChange = (newSchema: BaseSchema) => {
    setSchema(newSchema)
    console.log('Schema 更新:', newSchema)
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>设计器 - AbsoluteSection 嵌套测试</h2>

      <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#e7f3ff', borderRadius: '4px' }}>
        <h4>操作说明：</h4>
        <ul style={{ margin: '5px 0', fontSize: '14px' }}>
          <li>拖拽任意元素移动位置</li>
          <li>双击空白处添加按钮</li>
          <li>Shift + 双击添加容器</li>
          <li>Ctrl + 双击添加嵌套的 AbsoluteSection</li>
          <li>嵌套容器内也支持所有操作</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>AbsoluteSection 嵌套演示</h3>
        <AbsoluteSection
          schema={schema}
          width="700px"
          height="500px"
          onSchemaChange={handleSchemaChange}
        />

      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>原始 LcCanvas</h3>
        <LcCanvas scheme={schema}></LcCanvas>
      </div>

      <div>
        <h3>当前 Schema:</h3>
        <pre style={{
          backgroundColor: '#f8f9fa',
          padding: '10px',
          borderRadius: '4px',
          fontSize: '12px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {JSON.stringify(schema, null, 2)}
        </pre>
      </div>
    </div>
  )
}
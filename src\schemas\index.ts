
export type classType = string | Record<string, any>

export type styleType = string | Record<string, any>

export type expressionType = string

// TODO
export type SchemaBody = {}

export type ComponentProperty = {
  name:string,
  value?:any
}
export interface BaseSchema {
  /**
   * 组件名称
   */
  component: string
  /**
   * 组件唯一标识
   */
  dataId: string
  /**
   * 组件中文名
   */
  name?: string
  /**
   * 组件类名
   */
  class?: classType
  style?: styleType
  slots?: Array<string>
  body?: Record<string, BaseSchema>
  props?: ComponentProperty[]
}

export default abstract class AbstractSchema implements BaseSchema {
  abstract component: string
  abstract name: string
  abstract dataId: string
  abstract class?: classType
  abstract style?: styleType
  abstract props?:any;
  getTemplatePHP(){
    return ''
  }
}
import AbstractSchema, { type BaseSchema } from './index'

export interface ContainerProps {
  
}
export interface Container {
  
}

export interface ContainerSchema extends BaseSchema {
  compName: 'div'
  props?: ContainerProps
}


export class Container extends AbstractSchema {
  component: string = 'Container'
  name: string = '容器'
  reactive = false
  dataId = ''
  class = ''
  style = ''
  props = []
  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }

  getTemplatePHP(){
    return  `<div>`
  }

}

export default new Container([])

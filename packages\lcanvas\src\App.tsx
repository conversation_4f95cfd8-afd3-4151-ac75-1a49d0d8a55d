import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

import DemoWasm from './demo/demoWasm.tsx'
import Home from './view/home/<USER>'
import Demo from './Demo.tsx'
import DemoTree from './DemoTree.tsx';
import DemoComp from './DemoComp.tsx';
import DemoCompSimple from './DemoCompSimple.tsx';
import { CacheTestPage } from './CacheTestPage';

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { BladeRender } from './core/render/BladeRender.ts';

import type { BaseSchema } from 'nui';
import { useEffect, useState } from 'react';
import Image from 'nui/src/schemas/Image';

import Text from 'nui/src/schemas/Text';
import Icon from 'nui/src/schemas/icon';

const examleSchema:BaseSchema = {
  component:'div',
  id:'1',
  template:`@if($data["isAdmin"]) <p>管理员</p> @else <p>普通用户</p> @endif`
}

const textSchema: BaseSchema = {
  component: 'Text',
  id: 'text-demo',
  template: Text.getTemplatePHP()
}

const iconSchema: BaseSchema = {
  component: 'Icon',
  id: 'icon-demo',
  template: Icon.getTemplatePHP()
}

const imageSchema: BaseSchema = {
  component: 'Image',
  id: 'image-demo',
  template: Image.getTemplatePHP()
}



function App() {
  const [scheme, setSchema] = useState<BaseSchema>(examleSchema)

  useEffect(()=>{
    (async function(){
      const render = BladeRender.getInstance();
      // 为 Text 组件设置 props 变量
      await render.defineVariable("props", {
        content: '这是来自 nui 的文本组件',
        fontSize: '15px',
        color: '#e02894ff',
        fontWeight: 'bold'
      })
      await render.defineVariable("props", {
        name: '这是来自 nui 的图标组件',
        size: '20px',
        color: '#220817ff',
        rotate: 0,
        fontFamily: 'iconfont',
        spin: false
      })
      await render.defineVariable("props", {
        src: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
        alt: '占位图',
        width: '150px',
        height: 'auto',
        objectFit: 'cover',
        borderRadius: '8px'
      })
      setSchema(textSchema);
      setSchema(iconSchema);
      setSchema(imageSchema);
    })();
  },[])


  return (
    <>
      <Router>
        <Routes>
          {/* 默认显示简化版 Designer 界面 */}
          <Route path="/" element={<Demo />} />
          <Route path="/home" element={<Home />} />
          <Route path="/comp" element={<DemoComp />} />
          <Route path="/comp-simple" element={<DemoCompSimple />} />
          <Route path="/comp2" element={<DemoTree />} />
          <Route path="/cache-test" element={<CacheTestPage />} />
        </Routes>
      </Router>
    </>
  )
}

export default App

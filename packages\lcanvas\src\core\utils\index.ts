export const createGuid = ()=>{
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const createComponentId = ()=>{
  // TODO
  return Math.floor(Math.random()*1000)+''
}


export function Array2Object(arr: Array<{ name: string; value?: any }>) {
  const res: Record<string, any> = {}
  for (let item of arr) {
    res[item.name] = item.value
  }
  return res
}



/**
 * 基于定时器的节流函数
 * @param func 需要进行节流处理的函数
 * @param wait 节流的时间间隔，单位为毫秒
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timer:any = null;

  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (!timer) {
      func.apply(this, args);
      timer = setTimeout(() => {
        timer = null;
      }, wait);
    }
  } as T;
}
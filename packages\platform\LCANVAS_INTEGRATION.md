# LCanvas 集成到 Platform 项目

## 🎯 功能概述

已成功将 LCanvas 功能集成到 `packages/platform` 项目中，现在可以在 Designer 界面中使用 LCanvas 模式进行页面设计。

## 🚀 使用方法

### 1. 访问 Designer 页面

访问 `http://localhost:5173/` 或 `http://localhost:5173/simple` 进入 Designer 界面。

### 2. 切换到 LCanvas 模式

在工具栏中点击 **"LCanvas 模式"** 按钮，界面会切换到 LCanvas 渲染模式。

### 3. 添加组件

点击左侧的 **"🧩 模块"** 菜单，在 LCanvas 模式下会看到专门的组件：

#### LCanvas 专用组件
- **🃏 卡片** - 支持条件渲染的卡片组件
- **📋 列表** - 支持循环渲染的列表组件  
- **🔀 条件组件** - 基于权限的条件显示组件

#### 通用组件
- **📝 文字** - 基础文本组件
- **🔘 按钮** - 可点击按钮

### 4. 实时预览

选择组件后，schema 会自动更新，通过 `<BladeView schema={schema}></BladeView>` 进行实时渲染。

## 🔧 技术实现

### 核心组件

1. **LCanvasMode 组件**
   - 使用 `useBladeRender` Hook 管理 WASM 状态
   - 集成 `BladeView` 进行渲染
   - 包含缓存调试面板

2. **智能组件生成**
   - 根据模式自动生成不同的组件结构
   - LCanvas 模式生成带 Blade 模板语法的组件
   - 支持 PHP 变量和条件渲染

### Schema 结构

LCanvas 模式使用的 schema 结构：

```typescript
{
  component: 'div',
  id: '1',
  template: '<div><slot></slot></div>',
  body: [
    {
      component: 'div',
      id: 'card-123456',
      name: '卡片',
      template: `<div style="...">
        <h3>卡片标题</h3>
        @if($data['isAdmin'])
          <div>管理员可见内容</div>
        @else
          <div>普通用户内容</div>
        @endif
      </div>`
    }
  ]
}
```

## 🎨 组件示例

### 1. 卡片组件
```html
<div style="padding: 15px; margin: 10px; border: 1px solid #e5e5e5; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background: white;">
  <h3 style="margin: 0 0 10px 0; color: #333;">卡片标题</h3>
  <p style="margin: 0 0 10px 0; color: #666;">这是一个卡片组件</p>
  @if($data['isAdmin'])
    <div style="color: #28a745; font-size: 12px;">管理员可见内容</div>
  @else
    <div style="color: #6c757d; font-size: 12px;">普通用户内容</div>
  @endif
</div>
```

### 2. 列表组件
```html
<div style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
  <h4>列表标题</h4>
  <ul style="margin: 10px 0; padding-left: 20px;">
    @foreach([1, 2, 3, 4, 5] as $item)
      <li style="margin: 5px 0;">列表项 {{ $item }}</li>
    @endforeach
  </ul>
</div>
```

### 3. 条件组件
```html
<div style="padding: 15px; margin: 10px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
  <h4 style="margin: 0 0 10px 0; color: #856404;">条件渲染</h4>
  @if($data['isAdmin'])
    <div style="padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724;">
      ✅ 管理员权限：您可以看到这个内容
    </div>
  @else
    <div style="padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24;">
      ❌ 普通用户：权限不足
    </div>
  @endif
</div>
```

## 🔍 调试功能

### 缓存调试面板
- 右上角显示 WASM 缓存状态
- 可以手动清理缓存或强制刷新
- 显示加载时间和缓存信息

### 状态指示
- LCanvas 模式激活时显示绿色状态指示
- 加载过程中显示进度提示
- 错误时显示详细错误信息

## 📊 属性面板

右侧属性面板实时显示：
- 完整的 Schema 结构
- 组件统计信息
- 操作按钮（导出、清空、重置）

## 🎯 使用场景

### 1. 动态内容渲染
使用 Blade 模板语法创建动态内容，支持变量插值和条件渲染。

### 2. 权限控制
通过 `@if($data['isAdmin'])` 等条件语句实现基于权限的内容显示。

### 3. 循环渲染
使用 `@foreach` 循环渲染列表数据。

### 4. 实时预览
所见即所得的设计体验，修改 schema 立即看到渲染结果。

## 🚀 下一步计划

1. **扩展组件库** - 添加更多 LCanvas 专用组件
2. **变量管理** - 提供可视化的变量编辑界面
3. **模板编辑器** - 支持直接编辑 Blade 模板
4. **数据绑定** - 连接真实数据源
5. **导出功能** - 导出为可部署的页面

## 🎉 总结

LCanvas 模式已成功集成到 Platform 项目中，提供了强大的动态页面设计能力。通过左侧模块选择、中间实时预览、右侧属性查看的三栏布局，用户可以轻松创建复杂的动态页面。

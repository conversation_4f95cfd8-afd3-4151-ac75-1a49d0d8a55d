import { useState } from 'react'
import { Link } from 'react-router-dom'

function Home() {
  const [count, setCount] = useState(0)

  return (
    <div style={{ 
      padding: '40px', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <h1 style={{ 
        fontSize: '2.5rem', 
        marginBottom: '1rem', 
        color: '#333',
        textAlign: 'center'
      }}>
        LCanvas 项目
      </h1>
      
      <p style={{ 
        fontSize: '1.2rem', 
        color: '#666', 
        textAlign: 'center',
        marginBottom: '2rem'
      }}>
        基于 React + TypeScript + Vite 的可视化页面设计器
      </p>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '20px',
        marginBottom: '2rem'
      }}>
        <div style={{
          padding: '20px',
          border: '1px solid #e5e5e5',
          borderRadius: '8px',
          background: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>🎨 设计器</h3>
          <p style={{ margin: '0 0 15px 0', color: '#666', fontSize: '14px' }}>
            可视化页面设计工具，支持拖拽组件、实时预览
          </p>
          <Link 
            to="/" 
            style={{
              display: 'inline-block',
              padding: '8px 16px',
              background: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            进入设计器
          </Link>
        </div>

        <div style={{
          padding: '20px',
          border: '1px solid #e5e5e5',
          borderRadius: '8px',
          background: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>🧩 组件演示</h3>
          <p style={{ margin: '0 0 15px 0', color: '#666', fontSize: '14px' }}>
            BladeView 组件渲染演示，支持 Blade 模板语法
          </p>
          <Link 
            to="/comp" 
            style={{
              display: 'inline-block',
              padding: '8px 16px',
              background: '#28a745',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            查看演示
          </Link>
        </div>

        <div style={{
          padding: '20px',
          border: '1px solid #e5e5e5',
          borderRadius: '8px',
          background: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>🌳 树形控件</h3>
          <p style={{ margin: '0 0 15px 0', color: '#666', fontSize: '14px' }}>
            功能完整的树形控件，支持搜索、拖拽、复选框等
          </p>
          <Link 
            to="/comp2" 
            style={{
              display: 'inline-block',
              padding: '8px 16px',
              background: '#17a2b8',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            查看树形控件
          </Link>
        </div>
      </div>

      <div style={{
        padding: '20px',
        background: '#f8f9fa',
        borderRadius: '8px',
        marginBottom: '2rem'
      }}>
        <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>✨ 主要特性</h3>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#666' }}>
          <li style={{ marginBottom: '8px' }}>🎯 可视化拖拽设计</li>
          <li style={{ marginBottom: '8px' }}>🔧 组件化架构</li>
          <li style={{ marginBottom: '8px' }}>⚡ 实时预览</li>
          <li style={{ marginBottom: '8px' }}>🎨 多种布局模式</li>
          <li style={{ marginBottom: '8px' }}>📱 响应式设计</li>
          <li style={{ marginBottom: '8px' }}>🔍 搜索和过滤</li>
          <li style={{ marginBottom: '8px' }}>💾 数据持久化</li>
          <li style={{ marginBottom: '8px' }}>🚀 高性能渲染</li>
        </ul>
      </div>

      <div style={{
        textAlign: 'center',
        padding: '20px',
        background: 'white',
        borderRadius: '8px',
        border: '1px solid #e5e5e5'
      }}>
        <h3 style={{ margin: '0 0 15px 0', color: '#333' }}>🚀 快速开始</h3>
        <p style={{ margin: '0 0 20px 0', color: '#666' }}>
          点击计数器测试 React 状态管理
        </p>
        <button 
          onClick={() => setCount((count) => count + 1)}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#007bff'}
        >
          计数: {count}
        </button>
      </div>

      <footer style={{
        marginTop: '40px',
        textAlign: 'center',
        color: '#999',
        fontSize: '14px'
      }}>
        <p>基于 React 19 + TypeScript + Vite 构建</p>
      </footer>
    </div>
  )
}

export default Home

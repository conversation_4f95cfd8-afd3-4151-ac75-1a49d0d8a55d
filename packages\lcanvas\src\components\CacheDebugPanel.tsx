import React, { useState, useEffect } from 'react';
import { useBladeRender } from '../hooks/useBladeRender';

/**
 * 缓存调试面板组件
 * 用于显示和管理 WASM 缓存状态
 */
export const CacheDebugPanel: React.FC = () => {
  const { 
    isReady, 
    isLoading, 
    error, 
    cacheInfo, 
    clearCache, 
    forceRefresh, 
    getCacheInfo 
  } = useBladeRender();
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 定期更新缓存信息
  useEffect(() => {
    const interval = setInterval(() => {
      getCacheInfo();
    }, 5000); // 每5秒更新一次

    return () => clearInterval(interval);
  }, [getCacheInfo]);

  const handleForceRefresh = async () => {
    setRefreshing(true);
    try {
      await forceRefresh();
    } catch (err) {
      console.error('强制刷新失败:', err);
    } finally {
      setRefreshing(false);
    }
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
    return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`;
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#fff',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '10px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      zIndex: 1000,
      minWidth: '250px',
      fontSize: '12px'
    }}>
      <div 
        style={{ 
          cursor: 'pointer', 
          fontWeight: 'bold',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span>WASM 缓存状态</span>
        <span>{isExpanded ? '▼' : '▶'}</span>
      </div>

      <div style={{ marginTop: '8px' }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          marginBottom: '8px'
        }}>
          <span style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            background: isReady ? '#4CAF50' : isLoading ? '#FF9800' : '#F44336'
          }}></span>
          <span>
            {isReady ? '已就绪' : isLoading ? '加载中...' : '未就绪'}
          </span>
        </div>

        {error && (
          <div style={{ color: '#F44336', marginBottom: '8px' }}>
            错误: {error.message}
          </div>
        )}
      </div>

      {isExpanded && (
        <div style={{ marginTop: '10px', borderTop: '1px solid #eee', paddingTop: '10px' }}>
          {cacheInfo ? (
            <div style={{ marginBottom: '10px' }}>
              <div><strong>缓存详情:</strong></div>
              <div>版本: {cacheInfo.version}</div>
              <div>创建时间: {formatTime(cacheInfo.timestamp)}</div>
              <div>存在时长: {formatDuration(cacheInfo.age)}</div>
              <div>状态: {cacheInfo.isValid ? '✅ 有效' : '❌ 无效'}</div>
              {cacheInfo.isExpired && (
                <div style={{ color: '#FF9800' }}>⚠️ 已过期</div>
              )}
            </div>
          ) : (
            <div style={{ marginBottom: '10px', color: '#666' }}>
              无缓存数据
            </div>
          )}

          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            <button
              onClick={clearCache}
              style={{
                padding: '4px 8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                background: '#fff',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              清理缓存
            </button>
            
            <button
              onClick={handleForceRefresh}
              disabled={refreshing}
              style={{
                padding: '4px 8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                background: refreshing ? '#f0f0f0' : '#fff',
                cursor: refreshing ? 'not-allowed' : 'pointer',
                fontSize: '11px'
              }}
            >
              {refreshing ? '刷新中...' : '强制刷新'}
            </button>

            <button
              onClick={getCacheInfo}
              style={{
                padding: '4px 8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                background: '#fff',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              更新信息
            </button>
          </div>

          <div style={{ marginTop: '8px', fontSize: '10px', color: '#666' }}>
            提示: 缓存有效期为24小时
          </div>
        </div>
      )}
    </div>
  );
};

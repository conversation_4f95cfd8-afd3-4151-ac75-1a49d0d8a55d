import { getDefinedComponent, type BaseSchema } from "nui";
import { throttle } from "../utils";
import type { IRender } from "./IRender";
import parse from 'html-react-parser';
import { WASMCacheManager } from "../cache/WASMCacheManager";

export const VarScope = {
  /**
   * 跨应用
   */
  SESSION: 'session',
  /**
   * 全局变量，跨页面，跨组件
   */
  APPLICATION: 'global',
  /**
   * 页面级变量，跨组件
   */
  PAGE: 'data',
  /**
   * 组件级
   */
  COMPONENT: 'state'
} as const

export type ScopeType = typeof VarScope[keyof typeof VarScope];

/**
 * 变量作用域
 */
export type VariableScope = {
  scope: ScopeType,
  /** 
   * 作用域路径 
   * 如 scope = 'page'时，路径一般为: ".page_112233"，生成的PHP页面变量为:$["data"]["page_112233"]
   * 如 scope = 'state'时，路径一般为: ".page_112233.comp_112233"，生成的PHP页面变量为:$["data"]["page_112233"]["comp_112233"]
   */
  path: string;
}

export class BladeRender implements IRender {
  static isWASMReady: boolean = false;
  static instance: BladeRender
  php: any = null

  // 变量
  defineVariableLater: Array<{
    name: string,
    value: any,
    scope: VariableScope
  }> = [];


  onBladeOutput: any = null;

  constructor() {
    // 初始化时检查缓存状态
    this.initFromCache();
  }

  static getInstance() {
    if (!BladeRender.instance) {
      BladeRender.instance = new BladeRender();
    }
    return BladeRender.instance;
  }

  /**
   * 从缓存中初始化 WASM 状态
   */
  private initFromCache() {
    // 暂时禁用缓存恢复，避免 PHP 实例问题
    // 每次都重新加载以确保稳定性
    if (WASMCacheManager.isValidCache()) {
      console.log('发现有效缓存，但为了稳定性将重新加载 WASM');
    }

    // 清理缓存，强制重新加载
    WASMCacheManager.clearCache();
    BladeRender.isWASMReady = false;
  }

  /**
   * 手动清理缓存的公共方法
   */
  static clearCache() {
    WASMCacheManager.clearCache();
    BladeRender.isWASMReady = false;
  }

  /**
   * 获取缓存信息（用于调试）
   */
  static getCacheInfo() {
    return WASMCacheManager.getCacheInfo();
  }

  /**
   * 强制刷新缓存
   */
  static forceRefresh() {
    WASMCacheManager.forceRefresh();
    BladeRender.isWASMReady = false;
  }
  async loadWASM() {
    // 如果已经加载过且 PHP 实例存在，直接返回
    if (BladeRender.isWASMReady && this.php) {
      console.log('WASM 已经加载，跳过重复加载');
      return;
    }

    console.log('开始加载 WASM...')
    try {
      const host = `${window.location.protocol}//${window.location.host}`;
      console.log('host', host);
      const phpModule = await import(`${host}/lib/blade/PhpWeb.mjs`);
      this.php = new phpModule.PhpWeb();
      const phpBinary = await this.php.binary;
      const zipResponse = await fetch(`${host}/lib/blade/blade-template.zip`);
      if (!zipResponse.ok) {
        throw new Error('无法下载 ZIP 文件: ' + zipResponse.status);
      }
      const zipData = await zipResponse.arrayBuffer();
      phpBinary.FS.writeFile('./blade-template.zip', new Uint8Array(zipData));
      if (!phpBinary.FS.analyzePath('./workspace').exists) {
        phpBinary.FS.mkdir('./workspace');
      }
      await this.php.run(`<?php

                      $zip = new ZipArchive;
                      echo "zip....";

                      $reflection = new ReflectionClass('ZipArchive');
                      echo "类名：" . $reflection->getName() . "\\n";

                      $result = $zip->open("./blade-template.zip");
                      if ($result === TRUE) {
                          if (!$zip->extractTo("./workspace/")) {
                              exit(1);
                          }
                          $zip->close();
                      } else {
                          exit(1);
                      }
                      echo "after extract....";
                  ?>`);
      await this.php.run(`<?php include_once './workspace/blade-template/index.php'; ?>`);
      BladeRender.isWASMReady = true;

      // 保存 WASM 状态到缓存
      WASMCacheManager.saveToCache(true);
      console.log('WASM 加载完成，PHP 实例已创建');

      // define later
      for(let i=0;i<this.defineVariableLater.length;i++){
        let item = this.defineVariableLater[i];
        await this.createBladeVariable(item.name, item.value, item.scope);
      }
      this.defineVariableLater = [];
    } catch (error) {
      console.error('WASM 加载失败:', error);
      // 加载失败时清理缓存
      WASMCacheManager.clearCache();
      BladeRender.isWASMReady = false;
      throw error;
    }
  }

  /**
   * 
   * @param variableName 
   * @param variableValue 
   * @param scope 默认为page，即前启动的wasm页面会话
   * @returns 
   */
  async createBladeVariable(variableName: string, variableValue: any, scope: VariableScope = { scope: 'global', path: '' }) {
    let value = variableValue;
    let isObject = false;
    if (typeof variableValue == 'object') {
      isObject = true;
    } else {
      try {
        let obj = JSON.parse(variableValue);
        value = obj;
        isObject = true;
      } catch (err) { }
    }
    if (isObject) {
      value = `json_decode(\'${JSON.stringify(value)}\')`;
    }


    let code = `<?php echo "";
    if( !isset($page) ){
      $page=["data"=>[]];
    }
    if (!array_key_exists('data', $page) || !is_array($page['data'])) {
      $page['data'] = [];
    }
    `

    let key = `$data["${variableName}"]`
    switch (scope.scope) {
      case VarScope.SESSION:
        key = `$_SESSION['${variableName}']`
        code += `
          session_start();
          if (!isset(${key})) {
            ${key}= null;
          }
          ${key} = ${JSON.stringify(value)};
          session_end();
        `;
        break;
      default:
        code += `
          
          if (!isset($data)) {
              $data = $page["data"];

          }
          ${key} = ${value};
          $page["data"] = $data;
          echo '${variableName} is defined ';
          `
        break;
    }
    code += `
    var_dump($page);
    ?>`;
    //code = `<?php echo 'abc'; ?>`
    console.log('code===', code)
    return code;
  }

  async createBladeServiceVariable(variableName: string, serviceDefine: string, scope = 'page') {

  }

  /**
   * 渲染Blade 模板 或者执行PHP代码
   * @param phpstr 
   * @param blade 是否是Blade模板，默认是
   * @returns 
   */

  async executePhp(phpstr: string, blade: boolean = true): Promise<string> {
    console.log("blade", phpstr)
    let code = `<?php 
    $bladeString = '${phpstr}';
    $stringOutput = renderBladeString($bladeString, $page);
    echo $stringOutput;
    ?>`;

    if (!blade) {
      code = phpstr;
    }
    console.log('code===', code)

    return new Promise(async (resolve, reject) => {
      let php = this.php;
      let res: string[] = [];
      let timeoutId: NodeJS.Timeout | null = null;
      const onBladeOutput = (e: any) => {
        // console.log('php----', e.detail, new Date().valueOf());
        res = res.concat(e.detail || []);
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => {
          if (res.length) {
            if (timeoutId) clearTimeout(timeoutId)
            resolve(res.join(''));
          }

        }, 100);
      };

      this.onBladeOutput = onBladeOutput;

      // 确保 PHP 实例存在
      if (BladeRender.isWASMReady && this.php) {
        php = this.php;
        php.addEventListener('output', this.onBladeOutput);
        await php.run(code);
      } else {
        // 如果 WASM 未就绪或 PHP 实例不存在，重新加载
        await this.loadWASM();
        php = this.php;
        if (php) {
          php.addEventListener('output', this.onBladeOutput);
          await php.run(code);
        } else {
          throw new Error('PHP 实例创建失败');
        }
      }
    })
  }

  async prepare() {
    //await this.definedVariable('test','1')
  }
  async defineVariable(name: string, value: any) {
    if( !BladeRender.isWASMReady){
      this.defineVariableLater.push({
        name:name,
        value:value,
        scope:{scope:VarScope.SESSION, path:''}
      })
      return;
    }
    const code = await this.createBladeVariable(name, value);
    await this.executePhp(code, false)
  }


  mergeDefinedSchema (schema:BaseSchema ){
    const definedSchema = getDefinedComponent(schema.component);
    if( !definedSchema ) return schema;
    const newSchema = {...schema}
    if(definedSchema.props){
      newSchema.props = definedSchema.props.map((item:any)=>{
        return {
          ...item,
          value: schema.props?.[item.name]
        }
      })
    }
    return newSchema;
  }

  async render(schema: BaseSchema) {
    await this.prepare();
    let level = 0;

    const examleSchema: BaseSchema = {
      component: 'div',
      id: '1',
      template: `<slot></slot>`,
      body:[
        {
          $ref:'div',
          component:'div',
          id:'2',
          template:'<div data-id="2">111</div><div><slot id="3"></slot></div>',
          body:[
            {
              $ref:'Button',
              component:'div',
              id:'3',
              template:'<div id="btn3">button3</div>'
            }
          ]
        }
      ]
    }
    const renderTemplate = async (schema: BaseSchema) => {
      let html = '';
      if (schema.template) {
        html = await this.executePhp(schema.template);
      }
      const hasSlot = schema.template?.indexOf('<slot')!=-1
      if( hasSlot ){
        const replaceSlotTemplate = (template:string, schema: BaseSchema, level:number) => {
          // 若 schema 无模板，返回空字符串
          if (!schema.template) {
            return schema;
          }
          // 获取模板的 HTML 内容
          let templateHtml = ''
          templateHtml = schema.template;
          // 若没有子节点，直接返回模板 HTML
          if (!Array.isArray(schema.body) || schema.body.length === 0) {
            return schema;
          }

          // 存储子节点的 HTML 内容
          const bodyTemplates: Record<string, string> = {};
          if(Array.isArray(schema.body)){
            schema.body = schema.body.map((item)=>{
              return replaceSlotTemplate(schema.template||"", item, level+1) 
            })
            const alltemplate = (schema.body||[]).reduce((pre,cur)=>{
              return pre + (cur.template||'')
            },'');

            schema.template = schema.template.replace(/<slot[\s]*><\/slot>/g, alltemplate);
            schema.template = schema.template.replace(/<slot id="([^\"]+)"><\/slot>/g, (match, p1)=>{
              if( p1 ){
                let bodys:BaseSchema[] = [];
                if(Array.isArray(schema.body)){
                  bodys = schema.body.filter((item)=>{
                    return item.id == p1
                  })
                }
                let tpl = bodys.find((item)=>{
                  return item.id == p1
                })
                return tpl?.template??''
              }
            });
          }else{
            // debugger;
          }
          
          // for (const child of schema.body) {
          //   const childSchema = await replaceSlotTemplate(child, level++);
          //   child.template = childHtml
          //   bodyTemplates[child.id || 'default'] = childHtml;
          // }
          

          //debugger
          //return tmp
          //schema.template = tmp;
          return schema
        };
        let newSchema = await replaceSlotTemplate( '', schema, 0);
        return parse(newSchema.template ||'')
        
      }else{
        return parse(html);
      }
      
    }
    // const html = await this.executePhp(schema.template ?? '')
    // console.log('html', html)
    // const ele = parse(html, {
    //   replace: (dom:any) => {
        
    //   }
    // });
    // return ele;
    return renderTemplate(schema)
  }

  createElement = (element: BaseSchema) => {

  }
}
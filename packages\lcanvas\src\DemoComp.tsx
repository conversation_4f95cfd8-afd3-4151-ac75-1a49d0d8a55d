import { useState, useEffect } from 'react'
import axios from 'axios'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './core/env.ts'

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import DemoWasm from './demo/demoWasm.tsx';

const examleSchema: BaseSchema = {
  component: 'div',
  id: '1',
  template: `<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; background: #f8f9fa;">
    <h2>PHP Blade 语法测试</h2>
    <p>当前时间: {{ date('Y-m-d H:i:s') }}</p>
    <p>isAdmin 状态: {{ $data['isAdmin'] ? '是管理员' : '不是管理员' }}</p>
    @if($data['isAdmin'])
      <div style="color: #28a745; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 10px 0;">
        ✅ 管理员权限：您可以看到这个内容
      </div>
    @else
      <div style="color: #721c24; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 10px 0;">
        ❌ 普通用户：管理员内容已隐藏
      </div>
    @endif
    <p>调试信息: {{ json_encode($data) }}</p>
    <slot></slot>
  </div>`,
  body:[
    {
      component:'div',
      id:'2',
      template:`<div style="padding: 10px; margin: 10px; border: 1px solid #ddd; border-radius: 4px;">
        <h4>子组件测试</h4>
        <p>这是一个子组件</p>
        <p>PHP 变量测试: {{ $data["title"] ?? "无标题" }}</p>
        <slot></slot>
      </div>`,
      body:[
        {
          component:"div",
          id:'6',
          template:`<div style="padding: 5px; background: #e9ecef; border-radius: 3px; margin: 5px 0;">
            <strong>嵌套组件:</strong> {{ "Hello from nested component!" }}
          </div>`
        }
      ]
    }
  ]
}

function DemoComp() {
  const [scheme, setSchema] = useState({})

  useEffect(() => {
    (async function () {
      let render = BladeRender.getInstance();
      if (!BladeRender.isWASMReady) {
        await render.loadWASM();
      }

      // 定义测试变量
      await render.defineVariable("isAdmin", "1"); // 设置为管理员
      await render.defineVariable("data", {
        title: "DemoComp 测试页面",
        content: "这是通过 BladeView 渲染的内容",
        isAdmin: true,
        user: {
          name: "测试用户",
          role: "admin"
        }
      });
     
      setSchema(examleSchema);
      
    })()
  }, [])



  return (
    <>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoComp

import { useState, useEffect } from 'react'
import axios from 'axios'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './core/env.ts'

import './App.scss'
import { ReactView } from './view/render/ReactView.tsx';
import { BladeView } from './view/render/BladeView.tsx';
import { AbsoluteSection } from './ndesignUI/container/AbsoluteSection.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { BladeRender } from './core/render/BladeRender.ts';
import DemoWasm from './demo/demoWasm.tsx';

const examleSchema: BaseSchema = {
  component: 'div',
  id: '1',
  template: `<i></i><slot></slot>`,
  style:{
    default:[
      {
        name:':root',
        value:'border:1px solid red',
      }
    ]
  },
  body:[
    {
     
      component:'div',
      id:'2',
      template:'<div style= data-id="2"><slot></slot></div>',
      style:{
        default:[
          {
            name:':root ',
            value:'border:1px solid red',
          }
        ]
      },
      body:[
        {
          component:"div",
          id:'6',
          template:'<div data-id="div_xxx"></div>'
        },
        {
         
          component:'Button',
          id:'3',
          template:'<div data-id="3">button3</div>'
        },
        {
          component:'Button',
          id:'4',
          template:'<div data-id="4">button4</div>'
        }
      ]
    }
  ]
}

function DemoComp() {
  const [scheme, setSchema] = useState({})

  useEffect(() => {
    (async function () {
      let render = BladeRender.getInstance();
      if (!BladeRender.isWASMReady) {
        await render.loadWASM();
      }

      await render.defineVariable("isAdmin", "0")
     
      setSchema(examleSchema);
      
    })()
  }, [])



  return (
    <>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoComp

import { useState, useEffect } from 'react'
import './core/env.ts'

import './App.scss'
import { BladeView } from './view/render/BladeView.tsx';

import type { BaseSchema } from 'nui';
import Loading from './components/Loading.tsx';
import { useBladeRender } from './hooks/useBladeRender';
import { CacheDebugPanel } from './components/CacheDebugPanel';

const examleSchema: BaseSchema = {
  component: 'div',
  id: '1',
  template: `<i></i><slot></slot>`,
  style:{
    default:[
      {
        name:':root',
        value:'border:1px solid red',
      }
    ]
  },
  body:[
    {
     
      component:'div',
      id:'2',
      template:'<div style= data-id="2"><slot></slot></div>',
      style:{
        default:[
          {
            name:':root ',
            value:'border:1px solid red',
          }
        ]
      },
      body:[
        {
          component:"div",
          id:'6',
          template:'<div data-id="div_xxx"></div>'
        },
        {
         
          component:'Button',
          id:'3',
          template:'<div data-id="3">button3</div>'
        },
        {
          component:'Button',
          id:'4',
          template:'<div data-id="4">button4</div>'
        }
      ]
    }
  ]
}

function DemoComp() {
  const [scheme, setSchema] = useState({})
  const { isReady, isLoading, error, render, ensureReady, getCacheInfo } = useBladeRender();

  useEffect(() => {
    const initializeComponent = async () => {
      try {
        // 确保 WASM 已准备就绪
        await ensureReady();

        // 定义变量
        if (render) {
          await render.defineVariable("isAdmin", "0");
        }

        // 设置 schema
        setSchema(examleSchema);

        // 输出缓存信息（用于调试）
        console.log('缓存信息:', getCacheInfo());

      } catch (err) {
        console.error('DemoComp 初始化失败:', err);
      }
    };

    initializeComponent();
  }, [ensureReady, render, getCacheInfo]);

  // 显示加载状态
  if (isLoading) {
    return <Loading />;
  }

  // 显示错误状态
  if (error) {
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h3>加载失败</h3>
        <p>{error.message}</p>
        <button onClick={() => window.location.reload()}>重新加载</button>
      </div>
    );
  }

  // 显示主要内容
  return (
    <>
      <CacheDebugPanel />
      <div style={{ padding: '10px', background: '#f0f0f0', marginBottom: '10px' }}>
        <small>
          WASM 状态: {isReady ? '✅ 已就绪' : '❌ 未就绪'}
          {isReady && ' (从缓存恢复)'}
        </small>
      </div>
      <BladeView schema={scheme}></BladeView>
    </>
  )
}

export default DemoComp

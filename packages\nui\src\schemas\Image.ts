import { AbstractSchema, type BaseSchema } from './index'

export interface ImageProps {
  src?: string
  alt?: string
  width?: string | number
  height?: string | number
  objectFit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  borderRadius?: string | number
  lazy?: boolean
  placeholder?: string
}

export interface ImageSchema extends BaseSchema {
  compName: 'image'
  props?: ImageProps
}

export class Image extends AbstractSchema {
  component: string = 'Image'
  name: string = '图片'
  reactive = false
  id = ''
  class = ''
  style = ''
  props = []
  template?: string | undefined;

  constructor(props: any[]) {
    super();
    this.props = (props || []) as any;
  }


  getTemplatePHP() {
    return '<img class="image-element" data-id="{{ $data["props"]->dataId ?? $id ?? "" }}" src="{{ $data["props"]->src ?? "https://via.placeholder.com/300x200" }}" alt="{{ $data["props"]->alt ?? "图片" }}" style="width: {{ $data["props"]->width ?? "auto" }}; height: {{ $data["props"]->height ?? "auto" }}; object-fit: {{ $data["props"]->objectFit ?? "cover" }}; border-radius: {{ $data["props"]->borderRadius ?? "0" }}; display: block; max-width: 100%;" @if($data["props"]->lazy ?? false) loading="lazy" @endif />'
  }
}

export default new Image([
  {
    ref: 'src',
    label: '图片地址',
    format: 'string',
    default: 'https://via.placeholder.com/300x200',
    bindable: true,
  },
  {
    ref: 'alt',
    label: '替代文本',
    format: 'string',
    default: '图片',
    bindable: true,
  },
  {
    ref: 'width',
    label: '宽度',
    format: 'string',
    default: 'auto',
    bindable: true,
  },
  {
    ref: 'height',
    label: '高度',
    format: 'string',
    default: 'auto',
    bindable: true,
  },
  {
    ref: 'objectFit',
    label: '填充方式',
    format: 'string',
    default: 'cover',
    bindable: true,
  },
  {
    ref: 'borderRadius',
    label: '圆角',
    format: 'string',
    default: '0',
    bindable: true,
  },
  {
    ref: 'lazy',
    label: '懒加载',
    format: 'boolean',
    default: false,
    bindable: true,
  }
])